2025-07-11 23:26:45.726 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-11 23:26:45.731 | Loaded RSA public key for plugin verification
2025-07-11 23:26:45.777 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-11 23:26:45.777 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-11 23:26:45.777 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-11 23:26:45.777 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-11 23:26:45.781 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-11 23:26:45.781 | Using Consul URL: consul:8500
2025-07-11 23:26:45.837 | Initialized 6 predefined roles: coordinator, researcher, creative, critic, executor, domain_expert
2025-07-11 23:26:45.878 | AgentSet initialized with fixed ID: primary-agentset
2025-07-11 23:26:45.890 | AgentSet application running on agentset:5100
2025-07-11 23:26:45.893 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-11 23:26:45.967 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-11 23:26:45.980 | Service primary-agentset registered with Consul
2025-07-11 23:26:45.985 | Successfully registered AgentSet with Consul
2025-07-11 23:26:45.995 | AgentSet registered successfully with PostOffice
2025-07-11 23:26:46.046 | Loaded 1 agent specializations
2025-07-11 23:26:46.922 | Loaded 12 knowledge domains
2025-07-11 23:26:46.934 | Loaded 0 knowledge items
2025-07-11 23:26:59.332 | Connected to RabbitMQ
2025-07-11 23:26:59.337 | Channel created successfully
2025-07-11 23:26:59.337 | RabbitMQ channel ready
2025-07-11 23:26:59.391 | Connection test successful - RabbitMQ connection is stable
2025-07-11 23:26:59.391 | Creating queue: agentset-primary-agentset
2025-07-11 23:26:59.401 | Binding queue to exchange: stage7
2025-07-11 23:26:59.415 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-12 00:23:46.081 | Created ServiceTokenManager for AgentSet
2025-07-12 00:23:46.107 | Adding agent with req.body {
2025-07-12 00:23:46.107 |   agentId: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:23:46.107 |   actionVerb: 'ACCOMPLISH',
2025-07-12 00:23:46.107 |   inputs: { _type: 'Map', entries: [ [Array] ] },
2025-07-12 00:23:46.107 |   missionId: 'bbceb906-037e-47a4-8ada-4e0c18a0ad8e',
2025-07-12 00:23:46.107 |   missionContext: ''
2025-07-12 00:23:46.107 | }
2025-07-12 00:23:46.107 | Adding agent with inputs { _type: 'Map', entries: [ [ 'goal', [Object] ] ] }
2025-07-12 00:23:46.108 | addAgent provided inputs: { _type: 'Map', entries: [ [ 'goal', [Object] ] ] }
2025-07-12 00:23:46.108 | addAgent inputsMap: Map(1) {
2025-07-12 00:23:46.108 |   'goal' => {
2025-07-12 00:23:46.108 |     inputName: 'goal',
2025-07-12 00:23:46.108 |     value: 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.',
2025-07-12 00:23:46.108 |     valueType: 'string',
2025-07-12 00:23:46.108 |     args: {}
2025-07-12 00:23:46.108 |   }
2025-07-12 00:23:46.108 | }
2025-07-12 00:23:46.112 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-12 00:23:46.112 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-12 00:23:46.112 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-12 00:23:46.112 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-12 00:23:46.117 | Attempting to register with Consul (attempt 1/10)...
2025-07-12 00:23:46.117 | Using Consul URL: consul:8500
2025-07-12 00:23:46.142 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 created. missionId=bbceb906-037e-47a4-8ada-4e0c18a0ad8e. Inputs: {}
2025-07-12 00:23:46.147 | Service CapabilitiesManager found via environment variable CAPABILITIESMANAGER_URL: capabilitiesmanager:5060
2025-07-12 00:23:46.147 | Service TrafficManager found via environment variable TRAFFICMANAGER_URL: trafficmanager:5080
2025-07-12 00:23:46.154 | [Agent ded32008-8dc5-49e7-bdc9-d32acba1c461] Set up checkpointing every 15 minutes.
2025-07-12 00:23:46.159 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-12 00:23:46.235 | Service ded32008-8dc5-49e7-bdc9-d32acba1c461 registered with Consul
2025-07-12 00:23:46.235 | Successfully registered AgentSet with Consul
2025-07-12 00:23:46.309 | Connected to RabbitMQ
2025-07-12 00:23:46.323 | Channel created successfully
2025-07-12 00:23:46.324 | RabbitMQ channel ready
2025-07-12 00:23:46.375 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-12 00:23:46.406 | Connection test successful - RabbitMQ connection is stable
2025-07-12 00:23:46.406 | Creating queue: agentset-ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 00:23:46.414 | AgentSet registered successfully with PostOffice
2025-07-12 00:23:46.425 | Binding queue to exchange: stage7
2025-07-12 00:23:46.461 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-12 00:23:47.179 | Service Brain discovered via service discovery: brain:5070
2025-07-12 00:23:47.205 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:23:47.209 | [AuthenticatedAxios] Request hpexqnuc6w: Failed after 1041ms: {
2025-07-12 00:23:47.209 |   status: 413,
2025-07-12 00:23:47.209 |   statusText: 'Payload Too Large',
2025-07-12 00:23:47.209 |   data: '<!DOCTYPE html>\n' +
2025-07-12 00:23:47.209 |     '<html lang="en">\n' +
2025-07-12 00:23:47.209 |     '<head>\n' +
2025-07-12 00:23:47.209 |     '<meta charset="utf-8">\n' +
2025-07-12 00:23:47.209 |     '<title>Error</title>\n' +
2025-07-12 00:23:47.209 |     '</head>\n' +
2025-07-12 00:23:47.209 |     '<body>\n' +
2025-07-12 00:23:47.209 |     '<pre>Payload Too Large</pre>\n' +
2025-07-12 00:23:47.209 |     '</body>\n' +
2025-07-12 00:23:47.209 |     '</html>\n',
2025-07-12 00:23:47.209 |   url: 'http://librarian:5040/storeData'
2025-07-12 00:23:47.209 | }
2025-07-12 00:23:47.606 | Service Engineer found via PostOffice: engineer:5050
2025-07-12 00:23:47.610 | Service MissionControl found via PostOffice: missioncontrol:5030
2025-07-12 00:23:47.610 | Service URLs: {
2025-07-12 00:23:47.610 |   capabilitiesManagerUrl: 'capabilitiesmanager:5060',
2025-07-12 00:23:47.610 |   brainUrl: 'brain:5070',
2025-07-12 00:23:47.610 |   trafficManagerUrl: 'trafficmanager:5080',
2025-07-12 00:23:47.610 |   librarianUrl: 'librarian:5040'
2025-07-12 00:23:47.610 | }
2025-07-12 00:23:47.610 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 initialized successfully. Status: running. Commencing main execution loop.
2025-07-12 00:23:47.611 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 initialized and commencing operations.
2025-07-12 00:23:47.612 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:23:47.614 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 notifying TrafficManager of status: running
2025-07-12 00:23:47.615 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type agentUpdate to trafficmanager
2025-07-12 00:23:47.625 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:23:47.626 | Successfully sent message to PostOffice: Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 initialized and commencing operations.
2025-07-12 00:23:47.633 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:23:47.891 | Event logged successfully: {"eventType":"agent_created","agentId":"ded32008-8dc5-49e7-bdc9-d32acba1c461","missionId":"bbceb906-037e-47a4-8ada-4e0c18a0ad8e","inputValues":{"_type":"Map","entries":[["goal",{"inputName":"goal","value":"Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","valueType":"string","args":{}}]]},"status":"initializing","timestamp":"2025-07-12T04:23:46.145Z"}
2025-07-12 00:23:47.900 | Event logged successfully: {"eventType":"step_created","stepId":"180e6c01-4cbf-4f4a-825a-e32e5be2d2fe","stepNo":1,"actionVerb":"ACCOMPLISH","inputValues":{"_type":"Map","entries":[["goal",{"inputName":"goal","value":"Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","valueType":"string","args":{}}]]},"inputReferences":{"_type":"Map","entries":[]},"dependencies":[],"status":"pending","description":"Initial mission step","timestamp":"2025-07-12T04:23:46.144Z"}
2025-07-12 00:23:48.086 | AgentSet received update from agent ded32008-8dc5-49e7-bdc9-d32acba1c461 with status running
2025-07-12 00:23:48.120 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:23:48.120 | Executing step ACCOMPLISH (180e6c01-4cbf-4f4a-825a-e32e5be2d2fe)...
2025-07-12 00:23:48.120 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Executing step: ACCOMPLISH - Initial mission step
2025-07-12 00:23:48.120 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:23:48.132 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:23:48.132 | Successfully sent message to PostOffice: Executing step: ACCOMPLISH - Initial mission step
2025-07-12 00:23:48.235 | Error saving agent specializations: AxiosError: Request failed with status code 413
2025-07-12 00:23:48.235 |     at settle (/usr/src/app/node_modules/axios/dist/node/axios.cjs:2049:12)
2025-07-12 00:23:48.235 |     at IncomingMessage.handleStreamEnd (/usr/src/app/node_modules/axios/dist/node/axios.cjs:3166:11)
2025-07-12 00:23:48.235 |     at IncomingMessage.emit (node:events:536:35)
2025-07-12 00:23:48.235 |     at endReadableNT (node:internal/streams/readable:1698:12)
2025-07-12 00:23:48.235 |     at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
2025-07-12 00:23:48.235 |     at Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4276:41)
2025-07-12 00:23:48.235 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12 00:23:48.235 |     at async Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4271:14)
2025-07-12 00:23:48.235 |     at async SpecializationFramework.saveSpecializations (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:67:13)
2025-07-12 00:23:48.235 |     at async SpecializationFramework.assignRole (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:156:9)
2025-07-12 00:23:48.235 |     at async AgentSet.addAgent (/usr/src/app/services/agentset/dist/AgentSet.js:776:21) {
2025-07-12 00:23:48.235 |   code: 'ERR_BAD_REQUEST',
2025-07-12 00:23:48.235 |   config: {
2025-07-12 00:23:48.235 |     transitional: {
2025-07-12 00:23:48.235 |       silentJSONParsing: true,
2025-07-12 00:23:48.235 |       forcedJSONParsing: true,
2025-07-12 00:23:48.235 |       clarifyTimeoutError: false
2025-07-12 00:23:48.235 |     },
2025-07-12 00:23:48.235 |     adapter: [ 'xhr', 'http', 'fetch' ],
2025-07-12 00:23:48.235 |     transformRequest: [ [Function: transformRequest] ],
2025-07-12 00:23:48.235 |     transformResponse: [ [Function: transformResponse] ],
2025-07-12 00:23:48.235 |     timeout: 30000,
2025-07-12 00:23:48.235 |     xsrfCookieName: 'XSRF-TOKEN',
2025-07-12 00:23:48.235 |     xsrfHeaderName: 'X-XSRF-TOKEN',
2025-07-12 00:23:48.235 |     maxContentLength: -1,
2025-07-12 00:23:48.235 |     maxBodyLength: -1,
2025-07-12 00:23:48.235 |     env: { FormData: [Function [FormData]], Blob: [class Blob] },
2025-07-12 00:23:48.235 |     validateStatus: [Function: validateStatus],
2025-07-12 00:23:48.235 |     headers: Object [AxiosHeaders] {
2025-07-12 00:23:48.236 |       Accept: 'application/json, text/plain, */*',
2025-07-12 00:23:48.236 |       'Content-Type': 'application/json',
2025-07-12 00:23:48.236 |       'Access-Control-Allow-Origin': '*',
2025-07-12 00:23:48.236 |       'X-Request-ID': 'hpexqnuc6w',
2025-07-12 00:23:48.236 |       Authorization: 'Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
2025-07-12 00:23:48.236 |       'User-Agent': 'axios/1.9.0',
2025-07-12 00:23:48.236 |       'Content-Length': '102534',
2025-07-12 00:23:48.236 |       'Accept-Encoding': 'gzip, compress, deflate, br'
2025-07-12 00:23:48.236 |     },
2025-07-12 00:23:48.236 |     method: 'post',
2025-07-12 00:23:48.236 |     url: 'http://librarian:5040/storeData',
2025-07-12 00:23:48.236 |     data: '{"id":"agent_specializations","data":[{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"agentId":"54138087-4115-44e5-af35-9f37383334cd","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T03:14:01.812Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T03:14:01.812Z"}},"1":{"agentId":"7a39664f-a97c-4014-a333-d6ab41830e60","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-26T19:36:19.624Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-26T19:36:19.624Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"a6e8c52b-ea6e-46cd-a8f8-0fdb833199a2","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T04:56:24.406Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T04:56:24.406Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"91c8bf91-3792-4310-9e11-bf62dcccf5a8","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T12:34:40.855Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T12:34:40.855Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"1a433c71-54b0-4ee8-aa22-1b125e7091d1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T18:24:07.540Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T18:24:07.540Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"e34a1868-db4f-448d-815c-0fea70b4db70","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T19:18:48.993Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T19:18:48.993Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"dff6a998-cdde-4329-8ded-7a13f8a28176","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T22:10:22.480Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T22:10:22.480Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"b1d0fe44-5c25-4a60-8696-5fefec4236e1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T17:53:23.197Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-30T17:53:23.197Z"}},"2":{"agentId":"aff51406-12b7-46ab-a1e5-a50dea02a464","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T19:26:55.855Z","performance":{"successRate":0,"'... 92534 more characters,
2025-07-12 00:23:48.236 |     allowAbsoluteUrls: true,
2025-07-12 00:23:48.236 |     _requestStartTime: 1752294226165,
2025-07-12 00:23:48.236 |     _retryCount: 1
2025-07-12 00:23:48.236 |   },
2025-07-12 00:23:48.236 |   request: <ref *1> ClientRequest {
2025-07-12 00:23:48.236 |     _events: [Object: null prototype] {
2025-07-12 00:23:48.236 |       abort: [Function (anonymous)],
2025-07-12 00:23:48.236 |       aborted: [Function (anonymous)],
2025-07-12 00:23:48.236 |       connect: [Function (anonymous)],
2025-07-12 00:23:48.236 |       error: [Function (anonymous)],
2025-07-12 00:23:48.236 |       socket: [Function (anonymous)],
2025-07-12 00:23:48.236 |       timeout: [Function (anonymous)],
2025-07-12 00:23:48.236 |       finish: [Function: requestOnFinish]
2025-07-12 00:23:48.236 |     },
2025-07-12 00:23:48.236 |     _eventsCount: 7,
2025-07-12 00:23:48.236 |     _maxListeners: undefined,
2025-07-12 00:23:48.236 |     outputData: [],
2025-07-12 00:23:48.236 |     outputSize: 0,
2025-07-12 00:23:48.236 |     writable: true,
2025-07-12 00:23:48.236 |     destroyed: true,
2025-07-12 00:23:48.236 |     _last: false,
2025-07-12 00:23:48.236 |     chunkedEncoding: false,
2025-07-12 00:23:48.236 |     shouldKeepAlive: true,
2025-07-12 00:23:48.236 |     maxRequestsOnConnectionReached: false,
2025-07-12 00:23:48.236 |     _defaultKeepAlive: true,
2025-07-12 00:23:48.236 |     useChunkedEncodingByDefault: true,
2025-07-12 00:23:48.236 |     sendDate: false,
2025-07-12 00:23:48.236 |     _removedConnection: false,
2025-07-12 00:23:48.236 |     _removedContLen: false,
2025-07-12 00:23:48.236 |     _removedTE: false,
2025-07-12 00:23:48.236 |     strictContentLength: false,
2025-07-12 00:23:48.236 |     _contentLength: '102534',
2025-07-12 00:23:48.236 |     _hasBody: true,
2025-07-12 00:23:48.236 |     _trailer: '',
2025-07-12 00:23:48.236 |     finished: true,
2025-07-12 00:23:48.236 |     _headerSent: true,
2025-07-12 00:23:48.236 |     _closed: true,
2025-07-12 00:23:48.236 |     socket: Socket {
2025-07-12 00:23:48.236 |       connecting: false,
2025-07-12 00:23:48.236 |       _hadError: false,
2025-07-12 00:23:48.236 |       _parent: null,
2025-07-12 00:23:48.236 |       _host: 'librarian',
2025-07-12 00:23:48.236 |       _closeAfterHandlingError: false,
2025-07-12 00:23:48.236 |       _events: [Object],
2025-07-12 00:23:48.236 |       _readableState: [ReadableState],
2025-07-12 00:23:48.236 |       _writableState: [WritableState],
2025-07-12 00:23:48.236 |       allowHalfOpen: false,
2025-07-12 00:23:48.236 |       _maxListeners: undefined,
2025-07-12 00:23:48.236 |       _eventsCount: 6,
2025-07-12 00:23:48.236 |       _sockname: null,
2025-07-12 00:23:48.236 |       _pendingData: null,
2025-07-12 00:23:48.236 |       _pendingEncoding: '',
2025-07-12 00:23:48.236 |       server: null,
2025-07-12 00:23:48.236 |       _server: null,
2025-07-12 00:23:48.236 |       timeout: 4000,
2025-07-12 00:23:48.236 |       parser: null,
2025-07-12 00:23:48.236 |       _httpMessage: null,
2025-07-12 00:23:48.236 |       [Symbol(async_id_symbol)]: -1,
2025-07-12 00:23:48.236 |       [Symbol(kHandle)]: [TCP],
2025-07-12 00:23:48.236 |       [Symbol(lastWriteQueueSize)]: 0,
2025-07-12 00:23:48.236 |       [Symbol(timeout)]: Timeout {
2025-07-12 00:23:48.236 |         _idleTimeout: 4000,
2025-07-12 00:23:48.236 |         _idlePrev: [TimersList],
2025-07-12 00:23:48.236 |         _idleNext: [Timeout],
2025-07-12 00:23:48.236 |         _idleStart: 3080075,
2025-07-12 00:23:48.236 |         _onTimeout: [Function: bound ],
2025-07-12 00:23:48.236 |         _timerArgs: undefined,
2025-07-12 00:23:48.236 |         _repeat: null,
2025-07-12 00:23:48.236 |         _destroyed: false,
2025-07-12 00:23:48.236 |         [Symbol(refed)]: false,
2025-07-12 00:23:48.236 |         [Symbol(kHasPrimitive)]: false,
2025-07-12 00:23:48.236 |         [Symbol(asyncId)]: 203514,
2025-07-12 00:23:48.236 |         [Symbol(triggerId)]: 203512
2025-07-12 00:23:48.236 |       },
2025-07-12 00:23:48.236 |       [Symbol(kBuffer)]: null,
2025-07-12 00:23:48.236 |       [Symbol(kBufferCb)]: null,
2025-07-12 00:23:48.236 |       [Symbol(kBufferGen)]: null,
2025-07-12 00:23:48.236 |       [Symbol(shapeMode)]: true,
2025-07-12 00:23:48.236 |       [Symbol(kCapture)]: false,
2025-07-12 00:23:48.236 |       [Symbol(kSetNoDelay)]: true,
2025-07-12 00:23:48.236 |       [Symbol(kSetKeepAlive)]: true,
2025-07-12 00:23:48.236 |       [Symbol(kSetKeepAliveInitialDelay)]: 1,
2025-07-12 00:23:48.236 |       [Symbol(kBytesRead)]: 0,
2025-07-12 00:23:48.236 |       [Symbol(kBytesWritten)]: 0
2025-07-12 00:23:48.236 |     },
2025-07-12 00:23:48.236 |     _header: 'POST /storeData HTTP/1.1\r\n' +
2025-07-12 00:23:48.236 |       'Accept: application/json, text/plain, */*\r\n' +
2025-07-12 00:23:48.236 |       'Content-Type: application/json\r\n' +
2025-07-12 00:23:48.236 |       'Access-Control-Allow-Origin: *\r\n' +
2025-07-12 00:23:48.236 |       'X-Request-ID: hpexqnuc6w\r\n' +
2025-07-12 00:23:48.236 |       'Authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n' +
2025-07-12 00:23:48.236 |       'User-Agent: axios/1.9.0\r\n' +
2025-07-12 00:23:48.236 |       'Content-Length: 102534\r\n' +
2025-07-12 00:23:48.236 |       'Accept-Encoding: gzip, compress, deflate, br\r\n' +
2025-07-12 00:23:48.236 |       'Host: librarian:5040\r\n' +
2025-07-12 00:23:48.236 |       'Connection: keep-alive\r\n' +
2025-07-12 00:23:48.236 |       '\r\n',
2025-07-12 00:23:48.236 |     _keepAliveTimeout: 0,
2025-07-12 00:23:48.236 |     _onPendingData: [Function: nop],
2025-07-12 00:23:48.236 |     agent: Agent {
2025-07-12 00:23:48.236 |       _events: [Object: null prototype],
2025-07-12 00:23:48.236 |       _eventsCount: 2,
2025-07-12 00:23:48.236 |       _maxListeners: undefined,
2025-07-12 00:23:48.236 |       defaultPort: 80,
2025-07-12 00:23:48.236 |       protocol: 'http:',
2025-07-12 00:23:48.236 |       options: [Object: null prototype],
2025-07-12 00:23:48.236 |       requests: [Object: null prototype] {},
2025-07-12 00:23:48.236 |       sockets: [Object: null prototype],
2025-07-12 00:23:48.236 |       freeSockets: [Object: null prototype],
2025-07-12 00:23:48.236 |       keepAliveMsecs: 1000,
2025-07-12 00:23:48.236 |       keepAlive: true,
2025-07-12 00:23:48.236 |       maxSockets: Infinity,
2025-07-12 00:23:48.236 |       maxFreeSockets: 256,
2025-07-12 00:23:48.236 |       scheduling: 'lifo',
2025-07-12 00:23:48.236 |       maxTotalSockets: Infinity,
2025-07-12 00:23:48.236 |       totalSocketCount: 13,
2025-07-12 00:23:48.236 |       [Symbol(shapeMode)]: false,
2025-07-12 00:23:48.236 |       [Symbol(kCapture)]: false
2025-07-12 00:23:48.236 |     },
2025-07-12 00:23:48.236 |     socketPath: undefined,
2025-07-12 00:23:48.236 |     method: 'POST',
2025-07-12 00:23:48.236 |     maxHeaderSize: undefined,
2025-07-12 00:23:48.236 |     insecureHTTPParser: undefined,
2025-07-12 00:23:48.236 |     joinDuplicateHeaders: undefined,
2025-07-12 00:23:48.236 |     path: '/storeData',
2025-07-12 00:23:48.236 |     _ended: true,
2025-07-12 00:23:48.236 |     res: IncomingMessage {
2025-07-12 00:23:48.237 |       _events: [Object],
2025-07-12 00:23:48.237 |       _readableState: [ReadableState],
2025-07-12 00:23:48.237 |       _maxListeners: undefined,
2025-07-12 00:23:48.237 |       socket: null,
2025-07-12 00:23:48.237 |       httpVersionMajor: 1,
2025-07-12 00:23:48.237 |       httpVersionMinor: 1,
2025-07-12 00:23:48.237 |       httpVersion: '1.1',
2025-07-12 00:23:48.237 |       complete: true,
2025-07-12 00:23:48.237 |       rawHeaders: [Array],
2025-07-12 00:23:48.237 |       rawTrailers: [],
2025-07-12 00:23:48.237 |       joinDuplicateHeaders: undefined,
2025-07-12 00:23:48.237 |       aborted: false,
2025-07-12 00:23:48.237 |       upgrade: false,
2025-07-12 00:23:48.237 |       url: '',
2025-07-12 00:23:48.237 |       method: null,
2025-07-12 00:23:48.237 |       statusCode: 413,
2025-07-12 00:23:48.237 |       statusMessage: 'Payload Too Large',
2025-07-12 00:23:48.237 |       client: [Socket],
2025-07-12 00:23:48.237 |       _consuming: false,
2025-07-12 00:23:48.237 |       _dumped: false,
2025-07-12 00:23:48.237 |       req: [Circular *1],
2025-07-12 00:23:48.237 |       _eventsCount: 4,
2025-07-12 00:23:48.237 |       responseUrl: 'http://librarian:5040/storeData',
2025-07-12 00:23:48.237 |       redirects: [],
2025-07-12 00:23:48.237 |       [Symbol(shapeMode)]: true,
2025-07-12 00:23:48.237 |       [Symbol(kCapture)]: false,
2025-07-12 00:23:48.237 |       [Symbol(kHeaders)]: [Object],
2025-07-12 00:23:48.237 |       [Symbol(kHeadersCount)]: 16,
2025-07-12 00:23:48.237 |       [Symbol(kTrailers)]: null,
2025-07-12 00:23:48.237 |       [Symbol(kTrailersCount)]: 0
2025-07-12 00:23:48.237 |     },
2025-07-12 00:23:48.237 |     aborted: false,
2025-07-12 00:23:48.237 |     timeoutCb: null,
2025-07-12 00:23:48.237 |     upgradeOrConnect: false,
2025-07-12 00:23:48.237 |     parser: null,
2025-07-12 00:23:48.237 |     maxHeadersCount: null,
2025-07-12 00:23:48.237 |     reusedSocket: true,
2025-07-12 00:23:48.237 |     host: 'librarian',
2025-07-12 00:23:48.237 |     protocol: 'http:',
2025-07-12 00:23:48.237 |     _redirectable: Writable {
2025-07-12 00:23:48.237 |       _events: [Object],
2025-07-12 00:23:48.237 |       _writableState: [WritableState],
2025-07-12 00:23:48.237 |       _maxListeners: undefined,
2025-07-12 00:23:48.237 |       _options: [Object],
2025-07-12 00:23:48.237 |       _ended: true,
2025-07-12 00:23:48.237 |       _ending: true,
2025-07-12 00:23:48.237 |       _redirectCount: 0,
2025-07-12 00:23:48.237 |       _redirects: [],
2025-07-12 00:23:48.237 |       _requestBodyLength: 102534,
2025-07-12 00:23:48.237 |       _requestBodyBuffers: [],
2025-07-12 00:23:48.237 |       _eventsCount: 3,
2025-07-12 00:23:48.237 |       _onNativeResponse: [Function (anonymous)],
2025-07-12 00:23:48.237 |       _currentRequest: [Circular *1],
2025-07-12 00:23:48.237 |       _currentUrl: 'http://librarian:5040/storeData',
2025-07-12 00:23:48.237 |       _timeout: null,
2025-07-12 00:23:48.237 |       [Symbol(shapeMode)]: true,
2025-07-12 00:23:48.237 |       [Symbol(kCapture)]: false
2025-07-12 00:23:48.237 |     },
2025-07-12 00:23:48.237 |     [Symbol(shapeMode)]: false,
2025-07-12 00:23:48.237 |     [Symbol(kCapture)]: false,
2025-07-12 00:23:48.237 |     [Symbol(kBytesWritten)]: 0,
2025-07-12 00:23:48.237 |     [Symbol(kNeedDrain)]: false,
2025-07-12 00:23:48.237 |     [Symbol(corked)]: 0,
2025-07-12 00:23:48.237 |     [Symbol(kOutHeaders)]: [Object: null prototype] {
2025-07-12 00:23:48.237 |       accept: [Array],
2025-07-12 00:23:48.237 |       'content-type': [Array],
2025-07-12 00:23:48.237 |       'access-control-allow-origin': [Array],
2025-07-12 00:23:48.237 |       'x-request-id': [Array],
2025-07-12 00:23:48.237 |       authorization: [Array],
2025-07-12 00:23:48.237 |       'user-agent': [Array],
2025-07-12 00:23:48.237 |       'content-length': [Array],
2025-07-12 00:23:48.237 |       'accept-encoding': [Array],
2025-07-12 00:23:48.237 |       host: [Array]
2025-07-12 00:23:48.237 |     },
2025-07-12 00:23:48.237 |     [Symbol(errored)]: null,
2025-07-12 00:23:48.237 |     [Symbol(kHighWaterMark)]: 16384,
2025-07-12 00:23:48.237 |     [Symbol(kRejectNonStandardBodyWrites)]: false,
2025-07-12 00:23:48.237 |     [Symbol(kUniqueHeaders)]: null
2025-07-12 00:23:48.237 |   },
2025-07-12 00:23:48.237 |   response: {
2025-07-12 00:23:48.237 |     status: 413,
2025-07-12 00:23:48.237 |     statusText: 'Payload Too Large',
2025-07-12 00:23:48.237 |     headers: Object [AxiosHeaders] {
2025-07-12 00:23:48.237 |       'x-powered-by': 'Express',
2025-07-12 00:23:48.237 |       'content-security-policy': "default-src 'none'",
2025-07-12 00:23:48.237 |       'x-content-type-options': 'nosniff',
2025-07-12 00:23:48.237 |       'content-type': 'text/html; charset=utf-8',
2025-07-12 00:23:48.237 |       'content-length': '144',
2025-07-12 00:23:48.237 |       date: 'Sat, 12 Jul 2025 04:23:48 GMT',
2025-07-12 00:23:48.237 |       connection: 'keep-alive',
2025-07-12 00:23:48.237 |       'keep-alive': 'timeout=5'
2025-07-12 00:23:48.237 |     },
2025-07-12 00:23:48.237 |     config: {
2025-07-12 00:23:48.237 |       transitional: [Object],
2025-07-12 00:23:48.237 |       adapter: [Array],
2025-07-12 00:23:48.237 |       transformRequest: [Array],
2025-07-12 00:23:48.237 |       transformResponse: [Array],
2025-07-12 00:23:48.237 |       timeout: 30000,
2025-07-12 00:23:48.237 |       xsrfCookieName: 'XSRF-TOKEN',
2025-07-12 00:23:48.237 |       xsrfHeaderName: 'X-XSRF-TOKEN',
2025-07-12 00:23:48.237 |       maxContentLength: -1,
2025-07-12 00:23:48.237 |       maxBodyLength: -1,
2025-07-12 00:23:48.237 |       env: [Object],
2025-07-12 00:23:48.237 |       validateStatus: [Function: validateStatus],
2025-07-12 00:23:48.237 |       headers: [Object [AxiosHeaders]],
2025-07-12 00:23:48.237 |       method: 'post',
2025-07-12 00:23:48.237 |       url: 'http://librarian:5040/storeData',
2025-07-12 00:23:48.237 |       data: '{"id":"agent_specializations","data":[{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"agentId":"54138087-4115-44e5-af35-9f37383334cd","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T03:14:01.812Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T03:14:01.812Z"}},"1":{"agentId":"7a39664f-a97c-4014-a333-d6ab41830e60","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-26T19:36:19.624Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-26T19:36:19.624Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"a6e8c52b-ea6e-46cd-a8f8-0fdb833199a2","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T04:56:24.406Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T04:56:24.406Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"91c8bf91-3792-4310-9e11-bf62dcccf5a8","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T12:34:40.855Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T12:34:40.855Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"1a433c71-54b0-4ee8-aa22-1b125e7091d1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T18:24:07.540Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T18:24:07.540Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"e34a1868-db4f-448d-815c-0fea70b4db70","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T19:18:48.993Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T19:18:48.993Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"dff6a998-cdde-4329-8ded-7a13f8a28176","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T22:10:22.480Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T22:10:22.480Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"b1d0fe44-5c25-4a60-8696-5fefec4236e1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T17:53:23.197Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-30T17:53:23.197Z"}},"2":{"agentId":"aff51406-12b7-46ab-a1e5-a50dea02a464","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T19:26:55.855Z","performance":{"successRate":0,"'... 92534 more characters,
2025-07-12 00:23:48.237 |       allowAbsoluteUrls: true,
2025-07-12 00:23:48.237 |       _requestStartTime: 1752294226165,
2025-07-12 00:23:48.237 |       _retryCount: 1
2025-07-12 00:23:48.237 |     },
2025-07-12 00:23:48.237 |     request: <ref *1> ClientRequest {
2025-07-12 00:23:48.237 |       _events: [Object: null prototype],
2025-07-12 00:23:48.237 |       _eventsCount: 7,
2025-07-12 00:23:48.237 |       _maxListeners: undefined,
2025-07-12 00:23:48.237 |       outputData: [],
2025-07-12 00:23:48.237 |       outputSize: 0,
2025-07-12 00:23:48.237 |       writable: true,
2025-07-12 00:23:48.237 |       destroyed: true,
2025-07-12 00:23:48.237 |       _last: false,
2025-07-12 00:23:48.237 |       chunkedEncoding: false,
2025-07-12 00:23:48.237 |       shouldKeepAlive: true,
2025-07-12 00:23:48.237 |       maxRequestsOnConnectionReached: false,
2025-07-12 00:23:48.237 |       _defaultKeepAlive: true,
2025-07-12 00:23:48.237 |       useChunkedEncodingByDefault: true,
2025-07-12 00:23:48.237 |       sendDate: false,
2025-07-12 00:23:48.237 |       _removedConnection: false,
2025-07-12 00:23:48.237 |       _removedContLen: false,
2025-07-12 00:23:48.237 |       _removedTE: false,
2025-07-12 00:23:48.237 |       strictContentLength: false,
2025-07-12 00:23:48.237 |       _contentLength: '102534',
2025-07-12 00:23:48.237 |       _hasBody: true,
2025-07-12 00:23:48.237 |       _trailer: '',
2025-07-12 00:23:48.237 |       finished: true,
2025-07-12 00:23:48.237 |       _headerSent: true,
2025-07-12 00:23:48.237 |       _closed: true,
2025-07-12 00:23:48.237 |       socket: [Socket],
2025-07-12 00:23:48.237 |       _header: 'POST /storeData HTTP/1.1\r\n' +
2025-07-12 00:23:48.237 |         'Accept: application/json, text/plain, */*\r\n' +
2025-07-12 00:23:48.237 |         'Content-Type: application/json\r\n' +
2025-07-12 00:23:48.237 |         'Access-Control-Allow-Origin: *\r\n' +
2025-07-12 00:23:48.237 |         'X-Request-ID: hpexqnuc6w\r\n' +
2025-07-12 00:23:48.237 |         'Authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n' +
2025-07-12 00:23:48.237 |         'User-Agent: axios/1.9.0\r\n' +
2025-07-12 00:23:48.237 |         'Content-Length: 102534\r\n' +
2025-07-12 00:23:48.237 |         'Accept-Encoding: gzip, compress, deflate, br\r\n' +
2025-07-12 00:23:48.237 |         'Host: librarian:5040\r\n' +
2025-07-12 00:23:48.237 |         'Connection: keep-alive\r\n' +
2025-07-12 00:23:48.237 |         '\r\n',
2025-07-12 00:23:48.237 |       _keepAliveTimeout: 0,
2025-07-12 00:23:48.237 |       _onPendingData: [Function: nop],
2025-07-12 00:23:48.237 |       agent: [Agent],
2025-07-12 00:23:48.237 |       socketPath: undefined,
2025-07-12 00:23:48.237 |       method: 'POST',
2025-07-12 00:23:48.237 |       maxHeaderSize: undefined,
2025-07-12 00:23:48.237 |       insecureHTTPParser: undefined,
2025-07-12 00:23:48.237 |       joinDuplicateHeaders: undefined,
2025-07-12 00:23:48.237 |       path: '/storeData',
2025-07-12 00:23:48.237 |       _ended: true,
2025-07-12 00:23:48.237 |       res: [IncomingMessage],
2025-07-12 00:23:48.237 |       aborted: false,
2025-07-12 00:23:48.237 |       timeoutCb: null,
2025-07-12 00:23:48.237 |       upgradeOrConnect: false,
2025-07-12 00:23:48.237 |       parser: null,
2025-07-12 00:23:48.237 | Applied role Executor to agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 00:23:48.237 | Assigned default role executor to agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 00:23:48.238 |       maxHeadersCount: null,
2025-07-12 00:23:48.238 |       reusedSocket: true,
2025-07-12 00:23:48.238 |       host: 'librarian',
2025-07-12 00:23:48.238 |       protocol: 'http:',
2025-07-12 00:23:48.238 |       _redirectable: [Writable],
2025-07-12 00:23:48.238 |       [Symbol(shapeMode)]: false,
2025-07-12 00:23:48.238 |       [Symbol(kCapture)]: false,
2025-07-12 00:23:48.238 |       [Symbol(kBytesWritten)]: 0,
2025-07-12 00:23:48.238 |       [Symbol(kNeedDrain)]: false,
2025-07-12 00:23:48.238 |       [Symbol(corked)]: 0,
2025-07-12 00:23:48.238 |       [Symbol(kOutHeaders)]: [Object: null prototype],
2025-07-12 00:23:48.238 |       [Symbol(errored)]: null,
2025-07-12 00:23:48.238 |       [Symbol(kHighWaterMark)]: 16384,
2025-07-12 00:23:48.238 |       [Symbol(kRejectNonStandardBodyWrites)]: false,
2025-07-12 00:23:48.238 |       [Symbol(kUniqueHeaders)]: null
2025-07-12 00:23:48.238 |     },
2025-07-12 00:23:48.238 |     data: '<!DOCTYPE html>\n' +
2025-07-12 00:23:48.238 |       '<html lang="en">\n' +
2025-07-12 00:23:48.238 |       '<head>\n' +
2025-07-12 00:23:48.238 |       '<meta charset="utf-8">\n' +
2025-07-12 00:23:48.238 |       '<title>Error</title>\n' +
2025-07-12 00:23:48.238 |       '</head>\n' +
2025-07-12 00:23:48.238 |       '<body>\n' +
2025-07-12 00:23:48.238 |       '<pre>Payload Too Large</pre>\n' +
2025-07-12 00:23:48.238 |       '</body>\n' +
2025-07-12 00:23:48.238 |       '</html>\n'
2025-07-12 00:23:48.238 |   },
2025-07-12 00:23:48.238 |   status: 413
2025-07-12 00:23:48.238 | }
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 | **** REMEDIATION GUIDANCE ****
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 |     Error: Request failed with status code 413
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 |     Stack: AxiosError: Request failed with status code 413
2025-07-12 00:23:50.066 |     at settle (/usr/src/app/node_modules/axios/dist/node/axios.cjs:2049:12)
2025-07-12 00:23:50.066 |     at IncomingMessage.handleStreamEnd (/usr/src/app/node_modules/axios/dist/node/axios.cjs:3166:11)
2025-07-12 00:23:50.066 |     at IncomingMessage.emit (node:events:536:35)
2025-07-12 00:23:50.066 |     at endReadableNT (node:internal/streams/readable:1698:12)
2025-07-12 00:23:50.066 |     at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
2025-07-12 00:23:50.066 |     at Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4276:41)
2025-07-12 00:23:50.066 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12 00:23:50.066 |     at async Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4271:14)
2025-07-12 00:23:50.066 |     at async SpecializationFramework.saveSpecializations (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:67:13)
2025-07-12 00:23:50.066 |     at async SpecializationFramework.assignRole (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:156:9)
2025-07-12 00:23:50.066 |     at async AgentSet.addAgent (/usr/src/app/services/agentset/dist/AgentSet.js:776:21)
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 |     Remediation Guidance:
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.066 |     Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-12 00:23:50.066 | 
2025-07-12 00:23:50.067 | *******************************
2025-07-12 00:24:21.541 | [AuthenticatedAxios] Request pg7ucq4jc6b: Failed after 33398ms: {
2025-07-12 00:24:21.541 |   status: undefined,
2025-07-12 00:24:21.541 |   statusText: undefined,
2025-07-12 00:24:21.541 |   data: undefined,
2025-07-12 00:24:21.541 |   url: 'http://capabilitiesmanager:5060/executeAction'
2025-07-12 00:24:21.541 | }
2025-07-12 00:24:27.745 | Event logged successfully: {"eventType":"step_result","stepId":"180e6c01-4cbf-4f4a-825a-e32e5be2d2fe","stepNo":1,"actionVerb":"ACCOMPLISH","status":"completed","result":[{"success":true,"name":"plan","resultType":"plan","resultDescription":"A plan to: Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","result":[{"actionVerb":"THINK","inputReferences":{"prompt":{"value":"Based on the resume and LinkedIn profile of Chris Pravetz, identify potential job roles and industries that are a good fit.","valueType":"string"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Identify potential job roles and industries based on the resume and LinkedIn profile","outputs":{"jobRoles":"List of potential job roles and industries","keywords":"List of relevant keywords for job search"},"dependencies":{},"recommendedRole":"researcher"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"job search","valueType":"string"},"prompt":{"value":"Create a list of 10 people in the industry for job roles identified in step 1, to contact for informational interviews.","valueType":"string"}},"description":"Generate a list of people to contact for informational interviews","outputs":{"peopleToContact":"List of people to contact for informational interviews"},"dependencies":{"jobRoles":1},"recommendedRole":"researcher"},{"actionVerb":"THINK","inputReferences":{"prompt":{"valueType":"string","outputName":"peopleToContact"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Draft messages for informational interviews","outputs":{"draftMessages":"List of draft messages for informational interviews"},"dependencies":{"peopleToContact":2},"recommendedRole":"creative"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"job search","valueType":"string"},"prompt":{"value":"Find and list 10 job postings that match the job roles identified in step 1.","valueType":"string"}},"description":"Find and list job postings to apply to","outputs":{"jobPostings":"List of job postings to apply to"},"dependencies":{"jobRoles":1},"recommendedRole":"researcher"},{"actionVerb":"THINK","inputReferences":{"prompt":{"valueType":"string","outputName":"jobPostings"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Create customized resumes and cover letters for each job posting","outputs":{"customizedResumes":"List of customized resumes for each job posting","coverLetters":"List of cover letters for each job posting"},"dependencies":{"jobPostings":4},"recommendedRole":"creative"},{"actionVerb":"THINK","inputReferences":{"prompt":{"value":"Develop a plan to continuously monitor the internet for future job posts that match the target jobs.","valueType":"string"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Develop a plan to continuously monitor the internet for future job posts","outputs":{"monitoringPlan":"Plan to monitor the internet for future job posts"},"dependencies":{},"recommendedRole":"researcher"}],"mimeType":"application/json","logs":"2025-07-12 04:24:23,972 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-12 04:24:23,973 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- GET_USER_INPUT: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-12 04:24:23,973 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-12 04:24:23,973 - INFO - Querying Brain at brain:5070/chat with prompt length: 7701 chars\n2025-07-12 04:24:27,572 - INFO - Brain query successful with accuracy/text/code\n2025-07-12 04:24:27,573 - INFO - Model response received (attempt 1): {'type': 'PLAN', 'plan': [{'number': 1, 'actionVerb': 'THINK', 'inputs': {'prompt': {'value': 'Based on the resume and LinkedIn profile of Chris Pravetz, identify potential job roles and industries that are a good fit.', 'valueType': 'string'}, 'optimization': {'value': 'accuracy', 'valueType': 'string'}}, 'outputs': {'jobRoles': 'List of potential job roles and industries', 'keywords': 'List of relevant keywords for job search'}, 'description': 'Identify potential job roles and industries based...\n2025-07-12 04:24:27,573 - INFO - Successfully parsed top-level PLAN object. Plan length: 6\n2025-07-12 04:24:27,573 - INFO - Auto-fixed: added missing 'dependencies' field for step 1\n2025-07-12 04:24:27,574 - INFO - Auto-fixed step 3 input 'prompt': converted step reference to outputName='peopleToContact', added dependency\n2025-07-12 04:24:27,574 - INFO - Auto-fixed step 5 input 'prompt': converted step reference to outputName='jobPostings', added dependency\n2025-07-12 04:24:27,574 - INFO - Auto-fixed: added missing 'dependencies' field for step 6\n2025-07-12 04:24:27,609 - INFO - Successfully reported plan generation success to Brain (quality: 77)\n2025-07-12 04:24:27,609 - INFO - Successfully processed plan for goal: Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.\n"}],"dependencies":[],"timestamp":"2025-07-12T04:24:27.713Z"}
2025-07-12 00:24:27.745 | Saving work product for agent 180e6c01-4cbf-4f4a-825a-e32e5be2d2fe, step 180e6c01-4cbf-4f4a-825a-e32e5be2d2fe
2025-07-12 00:24:27.776 | Step ACCOMPLISH result: [
2025-07-12 00:24:27.776 |   {
2025-07-12 00:24:27.776 |     success: true,
2025-07-12 00:24:27.776 |     name: 'plan',
2025-07-12 00:24:27.776 |     resultType: 'plan',
2025-07-12 00:24:27.776 |     resultDescription: 'A plan to: Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.',
2025-07-12 00:24:27.776 |     result: [ [Object], [Object], [Object], [Object], [Object], [Object] ],
2025-07-12 00:24:27.776 |     mimeType: 'application/json',
2025-07-12 00:24:27.776 |     logs: "2025-07-12 04:24:23,972 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n" +
2025-07-12 00:24:27.776 |       `2025-07-12 04:24:23,973 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- GET_USER_INPUT: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n` +
2025-07-12 00:24:27.776 |       "2025-07-12 04:24:23,973 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n" +
2025-07-12 00:24:27.776 |       '2025-07-12 04:24:23,973 - INFO - Querying Brain at brain:5070/chat with prompt length: 7701 chars\n' +
2025-07-12 00:24:27.776 |       '2025-07-12 04:24:27,572 - INFO - Brain query successful with accuracy/text/code\n' +
2025-07-12 00:24:27.776 |       "2025-07-12 04:24:27,573 - INFO - Model response received (attempt 1): {'type': 'PLAN', 'plan': [{'number': 1, 'actionVerb': 'THINK', 'inputs': {'prompt': {'value': 'Based on the resume and LinkedIn profile of Chris Pravetz, identify potential job roles and industries that are a good fit.', 'valueType': 'string'}, 'optimization': {'value': 'accuracy', 'valueType': 'string'}}, 'outputs': {'jobRoles': 'List of potential job roles and industries', 'keywords': 'List of relevant keywords for job search'}, 'description': 'Identify potential job roles and industries based...\n" +
2025-07-12 00:24:27.776 |       '2025-07-12 04:24:27,573 - INFO - Successfully parsed top-level PLAN object. Plan length: 6\n' +
2025-07-12 00:24:27.776 |       "2025-07-12 04:24:27,573 - INFO - Auto-fixed: added missing 'dependencies' field for step 1\n" +
2025-07-12 00:24:27.776 |       "2025-07-12 04:24:27,574 - INFO - Auto-fixed step 3 input 'prompt': converted step reference to outputName='peopleToContact', added dependency\n" +
2025-07-12 00:24:27.776 |       "2025-07-12 04:24:27,574 - INFO - Auto-fixed step 5 input 'prompt': converted step reference to outputName='jobPostings', added dependency\n" +
2025-07-12 00:24:27.776 |       "2025-07-12 04:24:27,574 - INFO - Auto-fixed: added missing 'dependencies' field for step 6\n" +
2025-07-12 00:24:27.776 |       '2025-07-12 04:24:27,609 - INFO - Successfully reported plan generation success to Brain (quality: 77)\n' +
2025-07-12 00:24:27.776 |       '2025-07-12 04:24:27,609 - INFO - Successfully processed plan for goal: Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.\n'
2025-07-12 00:24:27.776 |   }
2025-07-12 00:24:27.776 | ]
2025-07-12 00:24:27.776 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Completed step: ACCOMPLISH
2025-07-12 00:24:27.776 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:27.776 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Generated a plan (direct array) with 6 steps
2025-07-12 00:24:27.776 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:27.810 | [Agent ded32008-8dc5-49e7-bdc9-d32acba1c461] Parsed plan for addStepsFromPlan: [{"actionVerb":"THINK","inputReferences":{"prompt":{"value":"Based on the resume and LinkedIn profile of Chris Pravetz, identify potential job roles and industries that are a good fit.","valueType":"string"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Identify potential job roles and industries based on the resume and LinkedIn profile","outputs":{"jobRoles":"List of potential job roles and industries","keywords":"List of relevant keywords for job search"},"dependencies":{},"recommendedRole":"researcher"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"job search","valueType":"string"},"prompt":{"value":"Create a list of 10 people in the industry for job roles identified in step 1, to contact for informational interviews.","valueType":"string"}},"description":"Generate a list of people to contact for informational interviews","outputs":{"peopleToContact":"List of people to contact for informational interviews"},"dependencies":{"jobRoles":1},"recommendedRole":"researcher"},{"actionVerb":"THINK","inputReferences":{"prompt":{"valueType":"string","outputName":"peopleToContact"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Draft messages for informational interviews","outputs":{"draftMessages":"List of draft messages for informational interviews"},"dependencies":{"peopleToContact":2},"recommendedRole":"creative"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"job search","valueType":"string"},"prompt":{"value":"Find and list 10 job postings that match the job roles identified in step 1.","valueType":"string"}},"description":"Find and list job postings to apply to","outputs":{"jobPostings":"List of job postings to apply to"},"dependencies":{"jobRoles":1},"recommendedRole":"researcher"},{"actionVerb":"THINK","inputReferences":{"prompt":{"valueType":"string","outputName":"jobPostings"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Create customized resumes and cover letters for each job posting","outputs":{"customizedResumes":"List of customized resumes for each job posting","coverLetters":"List of cover letters for each job posting"},"dependencies":{"jobPostings":4},"recommendedRole":"creative"},{"actionVerb":"THINK","inputReferences":{"prompt":{"value":"Develop a plan to continuously monitor the internet for future job posts that match the target jobs.","valueType":"string"},"optimization":{"value":"accuracy","valueType":"string"}},"description":"Develop a plan to continuously monitor the internet for future job posts","outputs":{"monitoringPlan":"Plan to monitor the internet for future job posts"},"dependencies":{},"recommendedRole":"researcher"}]
2025-07-12 00:24:27.810 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 notifying TrafficManager of status: running
2025-07-12 00:24:27.810 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:27.833 | Event logged successfully: {"eventType":"step_created","stepId":"e4b1d753-fea2-4ede-80d5-a23537a113cb","stepNo":2,"actionVerb":"THINK","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["prompt",{"inputName":"prompt","value":"Based on the resume and LinkedIn profile of Chris Pravetz, identify potential job roles and industries that are a good fit.","valueType":"string"}],["optimization",{"inputName":"optimization","value":"accuracy","valueType":"string"}]]},"dependencies":[],"status":"pending","description":"Identify potential job roles and industries based on the resume and LinkedIn profile","recommendedRole":"researcher","timestamp":"2025-07-12T04:24:27.787Z"}
2025-07-12 00:24:27.851 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:27.851 | Successfully sent message to PostOffice: Completed step: ACCOMPLISH
2025-07-12 00:24:27.852 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:27.852 | Successfully sent message to PostOffice: Generated a plan (direct array) with 6 steps
2025-07-12 00:24:27.853 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:27.857 | AgentSet received update from agent ded32008-8dc5-49e7-bdc9-d32acba1c461 with status running
2025-07-12 00:24:27.866 | Event logged successfully: {"eventType":"step_created","stepId":"33add0db-1e38-4b31-b601-61dcd6f858d6","stepNo":5,"actionVerb":"GENERATE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["ConversationType",{"inputName":"ConversationType","value":"job search","valueType":"string"}],["prompt",{"inputName":"prompt","value":"Find and list 10 job postings that match the job roles identified in step 1.","valueType":"string"}]]},"dependencies":[{"outputName":"jobRoles","sourceStepId":"e4b1d753-fea2-4ede-80d5-a23537a113cb","inputName":"jobRoles"}],"status":"pending","description":"Find and list job postings to apply to","recommendedRole":"researcher","timestamp":"2025-07-12T04:24:27.788Z"}
2025-07-12 00:24:27.868 | Event logged successfully: {"eventType":"step_created","stepId":"0ec620c5-ddf2-49dc-82e7-e2280606019f","stepNo":4,"actionVerb":"THINK","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["prompt",{"inputName":"prompt","outputName":"peopleToContact","valueType":"string"}],["optimization",{"inputName":"optimization","value":"accuracy","valueType":"string"}]]},"dependencies":[{"outputName":"peopleToContact","sourceStepId":"8859432c-4b74-4b78-8720-bc09c06ad862","inputName":"prompt"}],"status":"pending","description":"Draft messages for informational interviews","recommendedRole":"creative","timestamp":"2025-07-12T04:24:27.788Z"}
2025-07-12 00:24:27.876 | Event logged successfully: {"eventType":"step_created","stepId":"8859432c-4b74-4b78-8720-bc09c06ad862","stepNo":3,"actionVerb":"GENERATE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["ConversationType",{"inputName":"ConversationType","value":"job search","valueType":"string"}],["prompt",{"inputName":"prompt","value":"Create a list of 10 people in the industry for job roles identified in step 1, to contact for informational interviews.","valueType":"string"}]]},"dependencies":[{"outputName":"jobRoles","sourceStepId":"e4b1d753-fea2-4ede-80d5-a23537a113cb","inputName":"jobRoles"}],"status":"pending","description":"Generate a list of people to contact for informational interviews","recommendedRole":"researcher","timestamp":"2025-07-12T04:24:27.787Z"}
2025-07-12 00:24:27.898 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:27.907 | Event logged successfully: {"eventType":"step_created","stepId":"17b428b5-91b6-40c2-88ab-71913e1b4d7f","stepNo":7,"actionVerb":"THINK","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["prompt",{"inputName":"prompt","value":"Develop a plan to continuously monitor the internet for future job posts that match the target jobs.","valueType":"string"}],["optimization",{"inputName":"optimization","value":"accuracy","valueType":"string"}]]},"dependencies":[],"status":"pending","description":"Develop a plan to continuously monitor the internet for future job posts","recommendedRole":"researcher","timestamp":"2025-07-12T04:24:27.788Z"}
2025-07-12 00:24:27.907 | Event logged successfully: {"eventType":"step_created","stepId":"13bd4346-ef11-4445-b70d-9cf23e4c1a6f","stepNo":6,"actionVerb":"THINK","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["prompt",{"inputName":"prompt","outputName":"jobPostings","valueType":"string"}],["optimization",{"inputName":"optimization","value":"accuracy","valueType":"string"}]]},"dependencies":[{"outputName":"jobPostings","sourceStepId":"33add0db-1e38-4b31-b601-61dcd6f858d6","inputName":"prompt"}],"status":"pending","description":"Create customized resumes and cover letters for each job posting","recommendedRole":"creative","timestamp":"2025-07-12T04:24:27.788Z"}
2025-07-12 00:24:27.919 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 180e6c01-4cbf-4f4a-825a-e32e5be2d2fe
2025-07-12 00:24:27.943 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:27.944 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:27.944 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 notifying TrafficManager of status: running
2025-07-12 00:24:27.944 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:27.960 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:27.961 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:27.965 | AgentSet received update from agent ded32008-8dc5-49e7-bdc9-d32acba1c461 with status running
2025-07-12 00:24:28.001 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:28.026 | Executing step THINK (e4b1d753-fea2-4ede-80d5-a23537a113cb)...
2025-07-12 00:24:28.026 | Step e4b1d753-fea2-4ede-80d5-a23537a113cb recommends role researcher, but this agent has role executor
2025-07-12 00:24:28.027 | Attempting to delegate step e4b1d753-fea2-4ede-80d5-a23537a113cb to an agent with role researcher
2025-07-12 00:24:28.041 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-12 00:24:28.041 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-12 00:24:28.041 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-12 00:24:28.041 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-12 00:24:28.041 | Attempting to register with Consul (attempt 1/10)...
2025-07-12 00:24:28.041 | Using Consul URL: consul:8500
2025-07-12 00:24:28.045 | Agent c66266ad-88ed-4844-8929-78db18015b49 created. missionId=bbceb906-037e-47a4-8ada-4e0c18a0ad8e. Inputs: {}
2025-07-12 00:24:28.053 | Service CapabilitiesManager found via environment variable CAPABILITIESMANAGER_URL: capabilitiesmanager:5060
2025-07-12 00:24:28.053 | Service TrafficManager found via environment variable TRAFFICMANAGER_URL: trafficmanager:5080
2025-07-12 00:24:28.054 | [Agent c66266ad-88ed-4844-8929-78db18015b49] Set up checkpointing every 15 minutes.
2025-07-12 00:24:28.056 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-12 00:24:28.090 | Found agent c66266ad-88ed-4844-8929-78db18015b49 with role researcher
2025-07-12 00:24:28.095 | Event logged successfully: {"eventType":"step_created","stepId":"1f9951e6-441c-4149-a10e-de0501942782","stepNo":1,"actionVerb":"ACCOMPLISH","inputValues":{"_type":"Map","entries":[["goal",{"inputName":"goal","value":{"inputValue":"Act as a researcher agent","inputName":"goal","args":{}},"valueType":"any","args":{"goal":{"inputValue":"Act as a researcher agent","inputName":"goal","args":{}}}}]]},"inputReferences":{"_type":"Map","entries":[]},"dependencies":[],"status":"pending","description":"Initial mission step","timestamp":"2025-07-12T04:24:28.050Z"}
2025-07-12 00:24:28.095 | Event logged successfully: {"eventType":"agent_created","agentId":"c66266ad-88ed-4844-8929-78db18015b49","missionId":"bbceb906-037e-47a4-8ada-4e0c18a0ad8e","inputValues":{"_type":"Map","entries":[["goal",{"inputName":"goal","value":{"inputValue":"Act as a researcher agent","inputName":"goal","args":{}},"valueType":"any","args":{"goal":{"inputValue":"Act as a researcher agent","inputName":"goal","args":{}}}}]]},"status":"initializing","timestamp":"2025-07-12T04:24:28.052Z"}
2025-07-12 00:24:28.107 | [AuthenticatedAxios] Request n7xvduuytz9: Failed after 41ms: {
2025-07-12 00:24:28.107 |   status: 413,
2025-07-12 00:24:28.107 |   statusText: 'Payload Too Large',
2025-07-12 00:24:28.107 |   data: '<!DOCTYPE html>\n' +
2025-07-12 00:24:28.107 |     '<html lang="en">\n' +
2025-07-12 00:24:28.107 |     '<head>\n' +
2025-07-12 00:24:28.107 |     '<meta charset="utf-8">\n' +
2025-07-12 00:24:28.107 |     '<title>Error</title>\n' +
2025-07-12 00:24:28.107 |     '</head>\n' +
2025-07-12 00:24:28.107 |     '<body>\n' +
2025-07-12 00:24:28.107 |     '<pre>Payload Too Large</pre>\n' +
2025-07-12 00:24:28.107 |     '</body>\n' +
2025-07-12 00:24:28.107 |     '</html>\n',
2025-07-12 00:24:28.107 |   url: 'http://librarian:5040/storeData'
2025-07-12 00:24:28.107 | }
2025-07-12 00:24:28.123 | Agent c66266ad-88ed-4844-8929-78db18015b49 rejected delegation: Agent c66266ad-88ed-4844-8929-78db18015b49 is not running (status: initializing)
2025-07-12 00:24:28.123 | Delegation failed, executing step with current agent
2025-07-12 00:24:28.123 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Executing step: THINK - Identify potential job roles and industries based on the resume and LinkedIn profile
2025-07-12 00:24:28.123 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:28.127 | [Agent ded32008-8dc5-49e7-bdc9-d32acba1c461] useBrainForReasoning: Sending exchanges to Brain: [
2025-07-12 00:24:28.127 |   {
2025-07-12 00:24:28.127 |     "role": "user",
2025-07-12 00:24:28.127 |     "content": "Based on the resume and LinkedIn profile of Chris Pravetz, identify potential job roles and industries that are a good fit."
2025-07-12 00:24:28.127 |   }
2025-07-12 00:24:28.127 | ]
2025-07-12 00:24:28.139 | Service Brain discovered via service discovery: brain:5070
2025-07-12 00:24:28.154 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.154 | Successfully sent message to PostOffice: Executing step: THINK - Identify potential job roles and industries based on the resume and LinkedIn profile
2025-07-12 00:24:28.160 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-12 00:24:28.171 | Service MissionControl found via PostOffice: missioncontrol:5030
2025-07-12 00:24:28.171 | Service Engineer found via PostOffice: engineer:5050
2025-07-12 00:24:28.174 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:28.174 | Service URLs: {
2025-07-12 00:24:28.174 |   capabilitiesManagerUrl: 'capabilitiesmanager:5060',
2025-07-12 00:24:28.174 |   brainUrl: 'brain:5070',
2025-07-12 00:24:28.174 |   trafficManagerUrl: 'trafficmanager:5080',
2025-07-12 00:24:28.174 |   librarianUrl: 'librarian:5040'
2025-07-12 00:24:28.174 | }
2025-07-12 00:24:28.174 | Agent c66266ad-88ed-4844-8929-78db18015b49 initialized successfully. Status: running. Commencing main execution loop.
2025-07-12 00:24:28.174 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 saying: Agent c66266ad-88ed-4844-8929-78db18015b49 initialized and commencing operations.
2025-07-12 00:24:28.174 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type say to user
2025-07-12 00:24:28.174 | Agent c66266ad-88ed-4844-8929-78db18015b49 notifying TrafficManager of status: running
2025-07-12 00:24:28.174 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:28.186 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.186 | Successfully sent message to PostOffice: Agent c66266ad-88ed-4844-8929-78db18015b49 initialized and commencing operations.
2025-07-12 00:24:28.196 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:28.205 | Connected to RabbitMQ
2025-07-12 00:24:28.207 | AgentSet received update from agent c66266ad-88ed-4844-8929-78db18015b49 with status running
2025-07-12 00:24:28.233 | Service c66266ad-88ed-4844-8929-78db18015b49 registered with Consul
2025-07-12 00:24:28.233 | Successfully registered AgentSet with Consul
2025-07-12 00:24:28.235 | Channel created successfully
2025-07-12 00:24:28.235 | RabbitMQ channel ready
2025-07-12 00:24:28.262 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:28.262 | Executing step ACCOMPLISH (1f9951e6-441c-4149-a10e-de0501942782)...
2025-07-12 00:24:28.262 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 saying: Executing step: ACCOMPLISH - Initial mission step
2025-07-12 00:24:28.262 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type say to user
2025-07-12 00:24:28.276 | AgentSet registered successfully with PostOffice
2025-07-12 00:24:28.277 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.277 | Successfully sent message to PostOffice: Executing step: ACCOMPLISH - Initial mission step
2025-07-12 00:24:28.339 | Connection test successful - RabbitMQ connection is stable
2025-07-12 00:24:28.339 | Creating queue: agentset-c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:28.371 | Binding queue to exchange: stage7
2025-07-12 00:24:28.374 | Event logged successfully: {"eventType":"step_result","stepId":"e4b1d753-fea2-4ede-80d5-a23537a113cb","stepNo":2,"actionVerb":"THINK","status":"completed","result":[{"success":true,"name":"answer","resultType":"string","result":"Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.","resultDescription":"Brain reasoning output (text/text)","mimeType":"text/plain"}],"dependencies":[],"timestamp":"2025-07-12T04:24:28.366Z"}
2025-07-12 00:24:28.374 | Saving work product for agent e4b1d753-fea2-4ede-80d5-a23537a113cb, step e4b1d753-fea2-4ede-80d5-a23537a113cb
2025-07-12 00:24:28.390 | Step THINK result: [
2025-07-12 00:24:28.390 |   {
2025-07-12 00:24:28.390 |     success: true,
2025-07-12 00:24:28.390 |     name: 'answer',
2025-07-12 00:24:28.390 |     resultType: 'string',
2025-07-12 00:24:28.390 |     result: 'Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.',
2025-07-12 00:24:28.390 |     resultDescription: 'Brain reasoning output (text/text)',
2025-07-12 00:24:28.390 |     mimeType: 'text/plain'
2025-07-12 00:24:28.390 |   }
2025-07-12 00:24:28.390 | ]
2025-07-12 00:24:28.390 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Completed step: THINK
2025-07-12 00:24:28.390 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:28.395 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step e4b1d753-fea2-4ede-80d5-a23537a113cb
2025-07-12 00:24:28.397 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.397 | Successfully sent message to PostOffice: Completed step: THINK
2025-07-12 00:24:28.408 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:28.408 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 notifying TrafficManager of status: running
2025-07-12 00:24:28.409 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:28.421 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.425 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:28.435 | AgentSet received update from agent ded32008-8dc5-49e7-bdc9-d32acba1c461 with status running
2025-07-12 00:24:28.438 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-12 00:24:28.455 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:28.457 | Executing step GENERATE (8859432c-4b74-4b78-8720-bc09c06ad862)...
2025-07-12 00:24:28.457 | Step 8859432c-4b74-4b78-8720-bc09c06ad862 recommends role researcher, but this agent has role executor
2025-07-12 00:24:28.457 | Attempting to delegate step 8859432c-4b74-4b78-8720-bc09c06ad862 to an agent with role researcher
2025-07-12 00:24:28.462 | Found agent c66266ad-88ed-4844-8929-78db18015b49 with role researcher
2025-07-12 00:24:28.481 | Agent c66266ad-88ed-4844-8929-78db18015b49 received collaboration message: {
2025-07-12 00:24:28.481 |   id: 'aff2246d-b132-494a-aa74-df51cf7a395e',
2025-07-12 00:24:28.481 |   type: 'task_delegation',
2025-07-12 00:24:28.481 |   senderId: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:28.481 |   recipientId: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:28.482 |   payload: {
2025-07-12 00:24:28.482 |     id: '2bbaf094-34f7-4856-9b3f-95e103d43a4a',
2025-07-12 00:24:28.482 |     type: 'GENERATE',
2025-07-12 00:24:28.482 |     description: 'Generate a list of people to contact for informational interviews',
2025-07-12 00:24:28.482 |     inputs: undefined,
2025-07-12 00:24:28.482 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:28.482 |     delegatedTo: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:28.482 |     status: 'pending',
2025-07-12 00:24:28.482 |     createdAt: '2025-07-12T04:24:28.477Z',
2025-07-12 00:24:28.482 |     updatedAt: '2025-07-12T04:24:28.478Z',
2025-07-12 00:24:28.482 |     deadline: undefined,
2025-07-12 00:24:28.482 |     priority: 'normal'
2025-07-12 00:24:28.482 |   },
2025-07-12 00:24:28.482 |   content: {
2025-07-12 00:24:28.482 |     id: '2bbaf094-34f7-4856-9b3f-95e103d43a4a',
2025-07-12 00:24:28.482 |     type: 'GENERATE',
2025-07-12 00:24:28.482 |     description: 'Generate a list of people to contact for informational interviews',
2025-07-12 00:24:28.482 |     inputs: undefined,
2025-07-12 00:24:28.482 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:28.482 |     delegatedTo: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:28.482 |     status: 'pending',
2025-07-12 00:24:28.482 |     createdAt: '2025-07-12T04:24:28.477Z',
2025-07-12 00:24:28.482 |     updatedAt: '2025-07-12T04:24:28.478Z',
2025-07-12 00:24:28.482 |     deadline: undefined,
2025-07-12 00:24:28.482 |     priority: 'normal'
2025-07-12 00:24:28.482 |   },
2025-07-12 00:24:28.482 |   timestamp: '2025-07-12T04:24:28.481Z'
2025-07-12 00:24:28.482 | }
2025-07-12 00:24:28.485 | Successfully delegated step 8859432c-4b74-4b78-8720-bc09c06ad862 to agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:28.500 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 8859432c-4b74-4b78-8720-bc09c06ad862
2025-07-12 00:24:28.528 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:28.532 | Executing step THINK (0ec620c5-ddf2-49dc-82e7-e2280606019f)...
2025-07-12 00:24:28.532 | Step 0ec620c5-ddf2-49dc-82e7-e2280606019f recommends role creative, but this agent has role executor
2025-07-12 00:24:28.532 | Attempting to delegate step 0ec620c5-ddf2-49dc-82e7-e2280606019f to an agent with role creative
2025-07-12 00:24:28.539 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-12 00:24:28.539 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-12 00:24:28.539 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-12 00:24:28.539 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-12 00:24:28.539 | Attempting to register with Consul (attempt 1/10)...
2025-07-12 00:24:28.539 | Using Consul URL: consul:8500
2025-07-12 00:24:28.539 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 created. missionId=bbceb906-037e-47a4-8ada-4e0c18a0ad8e. Inputs: {}
2025-07-12 00:24:28.550 | Service CapabilitiesManager found via environment variable CAPABILITIESMANAGER_URL: capabilitiesmanager:5060
2025-07-12 00:24:28.550 | Service TrafficManager found via environment variable TRAFFICMANAGER_URL: trafficmanager:5080
2025-07-12 00:24:28.555 | [Agent b31d7887-fc71-4b2b-b1a9-5206e575be13] Set up checkpointing every 15 minutes.
2025-07-12 00:24:28.555 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-12 00:24:28.622 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.623 | Found agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with role creative
2025-07-12 00:24:28.635 | Service Brain discovered via service discovery: brain:5070
2025-07-12 00:24:28.659 | [AuthenticatedAxios] Request 6bio3qiw396: Failed after 106ms: {
2025-07-12 00:24:28.659 |   status: 413,
2025-07-12 00:24:28.659 |   statusText: 'Payload Too Large',
2025-07-12 00:24:28.659 |   data: '<!DOCTYPE html>\n' +
2025-07-12 00:24:28.659 |     '<html lang="en">\n' +
2025-07-12 00:24:28.659 |     '<head>\n' +
2025-07-12 00:24:28.659 |     '<meta charset="utf-8">\n' +
2025-07-12 00:24:28.659 |     '<title>Error</title>\n' +
2025-07-12 00:24:28.659 |     '</head>\n' +
2025-07-12 00:24:28.659 |     '<body>\n' +
2025-07-12 00:24:28.659 |     '<pre>Payload Too Large</pre>\n' +
2025-07-12 00:24:28.659 |     '</body>\n' +
2025-07-12 00:24:28.659 |     '</html>\n',
2025-07-12 00:24:28.659 |   url: 'http://librarian:5040/storeData'
2025-07-12 00:24:28.659 | }
2025-07-12 00:24:28.659 | Event logged successfully: {"eventType":"step_created","stepId":"2fdc4018-9e8b-428f-bb81-5e5fd1c30859","stepNo":1,"actionVerb":"ACCOMPLISH","inputValues":{"_type":"Map","entries":[["goal",{"inputName":"goal","value":{"inputValue":"Act as a creative agent","inputName":"goal","args":{}},"valueType":"any","args":{"goal":{"inputValue":"Act as a creative agent","inputName":"goal","args":{}}}}]]},"inputReferences":{"_type":"Map","entries":[]},"dependencies":[],"status":"pending","description":"Initial mission step","timestamp":"2025-07-12T04:24:28.540Z"}
2025-07-12 00:24:28.680 | Service MissionControl found via PostOffice: missioncontrol:5030
2025-07-12 00:24:28.680 | Service Engineer found via PostOffice: engineer:5050
2025-07-12 00:24:28.680 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 rejected delegation: Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 is not running (status: initializing)
2025-07-12 00:24:28.680 | Delegation failed, executing step with current agent
2025-07-12 00:24:28.680 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Executing step: THINK - Draft messages for informational interviews
2025-07-12 00:24:28.680 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:28.680 | Loading work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 8859432c-4b74-4b78-8720-bc09c06ad862
2025-07-12 00:24:28.694 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:28.694 | Service URLs: {
2025-07-12 00:24:28.694 |   capabilitiesManagerUrl: 'capabilitiesmanager:5060',
2025-07-12 00:24:28.694 |   brainUrl: 'brain:5070',
2025-07-12 00:24:28.694 |   trafficManagerUrl: 'trafficmanager:5080',
2025-07-12 00:24:28.694 |   librarianUrl: 'librarian:5040'
2025-07-12 00:24:28.694 | }
2025-07-12 00:24:28.694 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 initialized successfully. Status: running. Commencing main execution loop.
2025-07-12 00:24:28.694 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 saying: Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 initialized and commencing operations.
2025-07-12 00:24:28.694 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type say to user
2025-07-12 00:24:28.694 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 notifying TrafficManager of status: running
2025-07-12 00:24:28.694 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:28.704 | Event logged successfully: {"eventType":"agent_created","agentId":"b31d7887-fc71-4b2b-b1a9-5206e575be13","missionId":"bbceb906-037e-47a4-8ada-4e0c18a0ad8e","inputValues":{"_type":"Map","entries":[["goal",{"inputName":"goal","value":{"inputValue":"Act as a creative agent","inputName":"goal","args":{}},"valueType":"any","args":{"goal":{"inputValue":"Act as a creative agent","inputName":"goal","args":{}}}}]]},"status":"initializing","timestamp":"2025-07-12T04:24:28.541Z"}
2025-07-12 00:24:28.718 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-12 00:24:28.740 | Service b31d7887-fc71-4b2b-b1a9-5206e575be13 registered with Consul
2025-07-12 00:24:28.740 | Successfully registered AgentSet with Consul
2025-07-12 00:24:28.746 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.746 | Successfully sent message to PostOffice: Executing step: THINK - Draft messages for informational interviews
2025-07-12 00:24:28.746 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.747 | Successfully sent message to PostOffice: Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 initialized and commencing operations.
2025-07-12 00:24:28.749 | Event logged successfully: {"eventType":"step_result","stepId":"0ec620c5-ddf2-49dc-82e7-e2280606019f","stepNo":4,"actionVerb":"THINK","status":"completed","result":[{"success":false,"name":"error","resultType":"error","resultDescription":"Error in useBrainForReasoning","result":null,"error":"Prompt is required for THINK plugin","mimeType":"text/plain"}],"dependencies":[{"outputName":"peopleToContact","sourceStepId":"8859432c-4b74-4b78-8720-bc09c06ad862","inputName":"prompt"}],"timestamp":"2025-07-12T04:24:28.721Z"}
2025-07-12 00:24:28.749 | Saving work product for agent 0ec620c5-ddf2-49dc-82e7-e2280606019f, step 0ec620c5-ddf2-49dc-82e7-e2280606019f
2025-07-12 00:24:28.766 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:28.769 | Step THINK result: [
2025-07-12 00:24:28.769 |   {
2025-07-12 00:24:28.769 |     success: false,
2025-07-12 00:24:28.769 |     name: 'error',
2025-07-12 00:24:28.769 |     resultType: 'error',
2025-07-12 00:24:28.769 |     resultDescription: 'Error in useBrainForReasoning',
2025-07-12 00:24:28.769 |     result: null,
2025-07-12 00:24:28.769 |     error: 'Prompt is required for THINK plugin',
2025-07-12 00:24:28.769 |     mimeType: 'text/plain'
2025-07-12 00:24:28.769 |   }
2025-07-12 00:24:28.769 | ]
2025-07-12 00:24:28.769 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Completed step: THINK
2025-07-12 00:24:28.769 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:28.780 | AgentSet registered successfully with PostOffice
2025-07-12 00:24:28.783 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.783 | Successfully sent message to PostOffice: Completed step: THINK
2025-07-12 00:24:28.784 | AgentSet received update from agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with status running
2025-07-12 00:24:28.787 | Connected to RabbitMQ
2025-07-12 00:24:28.798 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 0ec620c5-ddf2-49dc-82e7-e2280606019f
2025-07-12 00:24:28.819 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:28.819 | Executing step ACCOMPLISH (2fdc4018-9e8b-428f-bb81-5e5fd1c30859)...
2025-07-12 00:24:28.819 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 saying: Executing step: ACCOMPLISH - Initial mission step
2025-07-12 00:24:28.819 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type say to user
2025-07-12 00:24:28.829 | Channel created successfully
2025-07-12 00:24:28.829 | RabbitMQ channel ready
2025-07-12 00:24:28.844 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.844 | Successfully sent message to PostOffice: Executing step: ACCOMPLISH - Initial mission step
2025-07-12 00:24:28.844 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:28.844 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:28.844 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 notifying TrafficManager of status: running
2025-07-12 00:24:28.844 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:28.881 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:28.893 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:28.907 | AgentSet received update from agent ded32008-8dc5-49e7-bdc9-d32acba1c461 with status running
2025-07-12 00:24:28.952 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:28.952 | Executing step GENERATE (33add0db-1e38-4b31-b601-61dcd6f858d6)...
2025-07-12 00:24:28.952 | Step 33add0db-1e38-4b31-b601-61dcd6f858d6 recommends role researcher, but this agent has role executor
2025-07-12 00:24:28.952 | Attempting to delegate step 33add0db-1e38-4b31-b601-61dcd6f858d6 to an agent with role researcher
2025-07-12 00:24:28.998 | Found agent c66266ad-88ed-4844-8929-78db18015b49 with role researcher
2025-07-12 00:24:29.025 | Agent c66266ad-88ed-4844-8929-78db18015b49 received collaboration message: {
2025-07-12 00:24:29.025 |   id: 'd019a170-83d2-4095-af32-b6a946abeec6',
2025-07-12 00:24:29.025 |   type: 'task_delegation',
2025-07-12 00:24:29.025 |   senderId: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.025 |   recipientId: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:29.025 |   payload: {
2025-07-12 00:24:29.025 |     id: 'e590e3da-9dff-4577-a4a4-10e5f0f92e38',
2025-07-12 00:24:29.025 |     type: 'GENERATE',
2025-07-12 00:24:29.025 |     description: 'Find and list job postings to apply to',
2025-07-12 00:24:29.025 |     inputs: undefined,
2025-07-12 00:24:29.025 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.025 |     delegatedTo: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:29.025 |     status: 'pending',
2025-07-12 00:24:29.025 |     createdAt: '2025-07-12T04:24:29.010Z',
2025-07-12 00:24:29.025 |     updatedAt: '2025-07-12T04:24:29.024Z',
2025-07-12 00:24:29.025 |     deadline: undefined,
2025-07-12 00:24:29.025 |     priority: 'normal'
2025-07-12 00:24:29.025 |   },
2025-07-12 00:24:29.025 |   content: {
2025-07-12 00:24:29.025 |     id: 'e590e3da-9dff-4577-a4a4-10e5f0f92e38',
2025-07-12 00:24:29.025 |     type: 'GENERATE',
2025-07-12 00:24:29.025 |     description: 'Find and list job postings to apply to',
2025-07-12 00:24:29.025 |     inputs: undefined,
2025-07-12 00:24:29.025 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.025 |     delegatedTo: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:29.025 |     status: 'pending',
2025-07-12 00:24:29.025 |     createdAt: '2025-07-12T04:24:29.010Z',
2025-07-12 00:24:29.025 |     updatedAt: '2025-07-12T04:24:29.024Z',
2025-07-12 00:24:29.025 |     deadline: undefined,
2025-07-12 00:24:29.025 |     priority: 'normal'
2025-07-12 00:24:29.025 |   },
2025-07-12 00:24:29.025 |   timestamp: '2025-07-12T04:24:29.024Z'
2025-07-12 00:24:29.025 | }
2025-07-12 00:24:29.027 | Connection test successful - RabbitMQ connection is stable
2025-07-12 00:24:29.027 | Creating queue: agentset-b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 00:24:29.029 | Successfully delegated step 33add0db-1e38-4b31-b601-61dcd6f858d6 to agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:29.056 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 33add0db-1e38-4b31-b601-61dcd6f858d6
2025-07-12 00:24:29.099 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:29.100 | Executing step THINK (13bd4346-ef11-4445-b70d-9cf23e4c1a6f)...
2025-07-12 00:24:29.100 | Step 13bd4346-ef11-4445-b70d-9cf23e4c1a6f recommends role creative, but this agent has role executor
2025-07-12 00:24:29.100 | Attempting to delegate step 13bd4346-ef11-4445-b70d-9cf23e4c1a6f to an agent with role creative
2025-07-12 00:24:29.130 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:29.133 | Error already analyzed: AxiosError:Request failed with status code 413
2025-07-12 00:24:29.147 | Error saving agent specializations: AxiosError: Request failed with status code 413
2025-07-12 00:24:29.147 |     at settle (/usr/src/app/node_modules/axios/dist/node/axios.cjs:2049:12)
2025-07-12 00:24:29.147 |     at IncomingMessage.handleStreamEnd (/usr/src/app/node_modules/axios/dist/node/axios.cjs:3166:11)
2025-07-12 00:24:29.147 |     at IncomingMessage.emit (node:events:536:35)
2025-07-12 00:24:29.147 |     at endReadableNT (node:internal/streams/readable:1698:12)
2025-07-12 00:24:29.147 |     at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
2025-07-12 00:24:29.147 |     at Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4276:41)
2025-07-12 00:24:29.147 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12 00:24:29.147 |     at async Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4271:14)
2025-07-12 00:24:29.147 |     at async SpecializationFramework.saveSpecializations (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:67:13)
2025-07-12 00:24:29.147 |     at async SpecializationFramework.assignRole (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:156:9)
2025-07-12 00:24:29.147 |     at async AgentSet.addAgentWithConfig (/usr/src/app/services/agentset/dist/AgentSet.js:857:21) {
2025-07-12 00:24:29.147 |   code: 'ERR_BAD_REQUEST',
2025-07-12 00:24:29.147 |   config: {
2025-07-12 00:24:29.147 |     transitional: {
2025-07-12 00:24:29.147 |       silentJSONParsing: true,
2025-07-12 00:24:29.147 |       forcedJSONParsing: true,
2025-07-12 00:24:29.147 |       clarifyTimeoutError: false
2025-07-12 00:24:29.147 |     },
2025-07-12 00:24:29.147 |     adapter: [ 'xhr', 'http', 'fetch' ],
2025-07-12 00:24:29.148 |     transformRequest: [ [Function: transformRequest] ],
2025-07-12 00:24:29.148 |     transformResponse: [ [Function: transformResponse] ],
2025-07-12 00:24:29.148 |     timeout: 30000,
2025-07-12 00:24:29.148 |     xsrfCookieName: 'XSRF-TOKEN',
2025-07-12 00:24:29.148 |     xsrfHeaderName: 'X-XSRF-TOKEN',
2025-07-12 00:24:29.148 |     maxContentLength: -1,
2025-07-12 00:24:29.148 |     maxBodyLength: -1,
2025-07-12 00:24:29.148 |     env: { FormData: [Function [FormData]], Blob: [class Blob] },
2025-07-12 00:24:29.148 |     validateStatus: [Function: validateStatus],
2025-07-12 00:24:29.148 |     headers: Object [AxiosHeaders] {
2025-07-12 00:24:29.148 |       Accept: 'application/json, text/plain, */*',
2025-07-12 00:24:29.148 |       'Content-Type': 'application/json',
2025-07-12 00:24:29.148 |       'Access-Control-Allow-Origin': '*',
2025-07-12 00:24:29.148 |       'X-Request-ID': 'n7xvduuytz9',
2025-07-12 00:24:29.148 |       Authorization: 'Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
2025-07-12 00:24:29.148 |       'User-Agent': 'axios/1.9.0',
2025-07-12 00:24:29.148 | Applied role Researcher to agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:29.148 | Assigned role researcher to agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:29.148 | Starting agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:29.148 | Agent c66266ad-88ed-4844-8929-78db18015b49 notifying TrafficManager of status: running
2025-07-12 00:24:29.148 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:29.148 |       'Content-Length': '102778',
2025-07-12 00:24:29.148 |       'Accept-Encoding': 'gzip, compress, deflate, br'
2025-07-12 00:24:29.148 |     },
2025-07-12 00:24:29.148 |     method: 'post',
2025-07-12 00:24:29.148 | Created new agent c66266ad-88ed-4844-8929-78db18015b49 with role researcher
2025-07-12 00:24:29.148 |     url: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.148 |     data: '{"id":"agent_specializations","data":[{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"agentId":"54138087-4115-44e5-af35-9f37383334cd","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T03:14:01.812Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T03:14:01.812Z"}},"1":{"agentId":"7a39664f-a97c-4014-a333-d6ab41830e60","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-26T19:36:19.624Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-26T19:36:19.624Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"a6e8c52b-ea6e-46cd-a8f8-0fdb833199a2","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T04:56:24.406Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T04:56:24.406Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"91c8bf91-3792-4310-9e11-bf62dcccf5a8","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T12:34:40.855Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T12:34:40.855Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"1a433c71-54b0-4ee8-aa22-1b125e7091d1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T18:24:07.540Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T18:24:07.540Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"e34a1868-db4f-448d-815c-0fea70b4db70","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T19:18:48.993Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T19:18:48.993Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"dff6a998-cdde-4329-8ded-7a13f8a28176","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T22:10:22.480Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T22:10:22.480Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"b1d0fe44-5c25-4a60-8696-5fefec4236e1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T17:53:23.197Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-30T17:53:23.197Z"}},"2":{"agentId":"aff51406-12b7-46ab-a1e5-a50dea02a464","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T19:26:55.855Z","performance":{"successRate":0,"'... 92778 more characters,
2025-07-12 00:24:29.148 |     allowAbsoluteUrls: true,
2025-07-12 00:24:29.148 |     _requestStartTime: 1752294268056,
2025-07-12 00:24:29.148 |     _retryCount: 1
2025-07-12 00:24:29.148 |   },
2025-07-12 00:24:29.148 |   request: <ref *1> ClientRequest {
2025-07-12 00:24:29.148 |     _events: [Object: null prototype] {
2025-07-12 00:24:29.148 |       abort: [Function (anonymous)],
2025-07-12 00:24:29.148 |       aborted: [Function (anonymous)],
2025-07-12 00:24:29.148 |       connect: [Function (anonymous)],
2025-07-12 00:24:29.148 |       error: [Function (anonymous)],
2025-07-12 00:24:29.148 |       socket: [Function (anonymous)],
2025-07-12 00:24:29.148 |       timeout: [Function (anonymous)],
2025-07-12 00:24:29.148 |       finish: [Function: requestOnFinish]
2025-07-12 00:24:29.148 |     },
2025-07-12 00:24:29.148 |     _eventsCount: 7,
2025-07-12 00:24:29.148 |     _maxListeners: undefined,
2025-07-12 00:24:29.148 |     outputData: [],
2025-07-12 00:24:29.148 |     outputSize: 0,
2025-07-12 00:24:29.148 |     writable: true,
2025-07-12 00:24:29.148 |     destroyed: true,
2025-07-12 00:24:29.148 |     _last: false,
2025-07-12 00:24:29.148 |     chunkedEncoding: false,
2025-07-12 00:24:29.148 |     shouldKeepAlive: true,
2025-07-12 00:24:29.148 |     maxRequestsOnConnectionReached: false,
2025-07-12 00:24:29.148 |     _defaultKeepAlive: true,
2025-07-12 00:24:29.148 |     useChunkedEncodingByDefault: true,
2025-07-12 00:24:29.148 |     sendDate: false,
2025-07-12 00:24:29.148 |     _removedConnection: false,
2025-07-12 00:24:29.148 |     _removedContLen: false,
2025-07-12 00:24:29.148 |     _removedTE: false,
2025-07-12 00:24:29.148 |     strictContentLength: false,
2025-07-12 00:24:29.148 |     _contentLength: '102778',
2025-07-12 00:24:29.148 |     _hasBody: true,
2025-07-12 00:24:29.148 |     _trailer: '',
2025-07-12 00:24:29.148 |     finished: true,
2025-07-12 00:24:29.148 |     _headerSent: true,
2025-07-12 00:24:29.148 |     _closed: true,
2025-07-12 00:24:29.148 |     socket: Socket {
2025-07-12 00:24:29.148 |       connecting: false,
2025-07-12 00:24:29.148 |       _hadError: false,
2025-07-12 00:24:29.148 |       _parent: null,
2025-07-12 00:24:29.148 |       _host: 'librarian',
2025-07-12 00:24:29.148 |       _closeAfterHandlingError: false,
2025-07-12 00:24:29.148 |       _events: [Object],
2025-07-12 00:24:29.148 |       _readableState: [ReadableState],
2025-07-12 00:24:29.148 |       _writableState: [WritableState],
2025-07-12 00:24:29.148 |       allowHalfOpen: false,
2025-07-12 00:24:29.148 |       _maxListeners: undefined,
2025-07-12 00:24:29.148 |       _eventsCount: 6,
2025-07-12 00:24:29.148 |       _sockname: null,
2025-07-12 00:24:29.148 |       _pendingData: null,
2025-07-12 00:24:29.148 |       _pendingEncoding: '',
2025-07-12 00:24:29.148 |       server: null,
2025-07-12 00:24:29.148 |       _server: null,
2025-07-12 00:24:29.148 |       timeout: 4000,
2025-07-12 00:24:29.148 |       parser: null,
2025-07-12 00:24:29.148 |       _httpMessage: null,
2025-07-12 00:24:29.148 |       [Symbol(async_id_symbol)]: -1,
2025-07-12 00:24:29.148 |       [Symbol(kHandle)]: [TCP],
2025-07-12 00:24:29.148 |       [Symbol(lastWriteQueueSize)]: 0,
2025-07-12 00:24:29.148 |       [Symbol(timeout)]: Timeout {
2025-07-12 00:24:29.148 |         _idleTimeout: 4000,
2025-07-12 00:24:29.148 |         _idlePrev: [TimersList],
2025-07-12 00:24:29.148 |         _idleNext: [Timeout],
2025-07-12 00:24:29.148 |         _idleStart: 3117652,
2025-07-12 00:24:29.148 |         _onTimeout: [Function: bound ],
2025-07-12 00:24:29.148 |         _timerArgs: undefined,
2025-07-12 00:24:29.148 |         _repeat: null,
2025-07-12 00:24:29.148 |         _destroyed: false,
2025-07-12 00:24:29.148 |         [Symbol(refed)]: false,
2025-07-12 00:24:29.148 |         [Symbol(kHasPrimitive)]: false,
2025-07-12 00:24:29.148 |         [Symbol(asyncId)]: 207613,
2025-07-12 00:24:29.148 |         [Symbol(triggerId)]: 207611
2025-07-12 00:24:29.148 |       },
2025-07-12 00:24:29.148 |       [Symbol(kBuffer)]: null,
2025-07-12 00:24:29.148 |       [Symbol(kBufferCb)]: null,
2025-07-12 00:24:29.148 |       [Symbol(kBufferGen)]: null,
2025-07-12 00:24:29.148 |       [Symbol(shapeMode)]: true,
2025-07-12 00:24:29.148 |       [Symbol(kCapture)]: false,
2025-07-12 00:24:29.148 |       [Symbol(kSetNoDelay)]: true,
2025-07-12 00:24:29.148 |       [Symbol(kSetKeepAlive)]: true,
2025-07-12 00:24:29.148 |       [Symbol(kSetKeepAliveInitialDelay)]: 1,
2025-07-12 00:24:29.148 |       [Symbol(kBytesRead)]: 0,
2025-07-12 00:24:29.148 |       [Symbol(kBytesWritten)]: 0
2025-07-12 00:24:29.148 |     },
2025-07-12 00:24:29.148 |     _header: 'POST /storeData HTTP/1.1\r\n' +
2025-07-12 00:24:29.148 |       'Accept: application/json, text/plain, */*\r\n' +
2025-07-12 00:24:29.148 |       'Content-Type: application/json\r\n' +
2025-07-12 00:24:29.148 |       'Access-Control-Allow-Origin: *\r\n' +
2025-07-12 00:24:29.148 |       'X-Request-ID: n7xvduuytz9\r\n' +
2025-07-12 00:24:29.148 |       'Authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n' +
2025-07-12 00:24:29.148 |       'User-Agent: axios/1.9.0\r\n' +
2025-07-12 00:24:29.148 |       'Content-Length: 102778\r\n' +
2025-07-12 00:24:29.148 |       'Accept-Encoding: gzip, compress, deflate, br\r\n' +
2025-07-12 00:24:29.148 |       'Host: librarian:5040\r\n' +
2025-07-12 00:24:29.148 |       'Connection: keep-alive\r\n' +
2025-07-12 00:24:29.148 |       '\r\n',
2025-07-12 00:24:29.148 |     _keepAliveTimeout: 0,
2025-07-12 00:24:29.148 |     _onPendingData: [Function: nop],
2025-07-12 00:24:29.148 |     agent: Agent {
2025-07-12 00:24:29.148 |       _events: [Object: null prototype],
2025-07-12 00:24:29.148 |       _eventsCount: 2,
2025-07-12 00:24:29.148 |       _maxListeners: undefined,
2025-07-12 00:24:29.148 |       defaultPort: 80,
2025-07-12 00:24:29.148 |       protocol: 'http:',
2025-07-12 00:24:29.148 |       options: [Object: null prototype],
2025-07-12 00:24:29.148 |       requests: [Object: null prototype] {},
2025-07-12 00:24:29.148 |       sockets: [Object: null prototype],
2025-07-12 00:24:29.148 |       freeSockets: [Object: null prototype],
2025-07-12 00:24:29.148 |       keepAliveMsecs: 1000,
2025-07-12 00:24:29.149 |       keepAlive: true,
2025-07-12 00:24:29.149 |       maxSockets: Infinity,
2025-07-12 00:24:29.149 |       maxFreeSockets: 256,
2025-07-12 00:24:29.149 |       scheduling: 'lifo',
2025-07-12 00:24:29.149 |       maxTotalSockets: Infinity,
2025-07-12 00:24:29.149 |       totalSocketCount: 20,
2025-07-12 00:24:29.149 |       [Symbol(shapeMode)]: false,
2025-07-12 00:24:29.149 |       [Symbol(kCapture)]: false
2025-07-12 00:24:29.149 |     },
2025-07-12 00:24:29.149 |     socketPath: undefined,
2025-07-12 00:24:29.149 |     method: 'POST',
2025-07-12 00:24:29.149 |     maxHeaderSize: undefined,
2025-07-12 00:24:29.149 |     insecureHTTPParser: undefined,
2025-07-12 00:24:29.149 |     joinDuplicateHeaders: undefined,
2025-07-12 00:24:29.149 |     path: '/storeData',
2025-07-12 00:24:29.149 |     _ended: true,
2025-07-12 00:24:29.149 |     res: IncomingMessage {
2025-07-12 00:24:29.149 |       _events: [Object],
2025-07-12 00:24:29.149 |       _readableState: [ReadableState],
2025-07-12 00:24:29.149 |       _maxListeners: undefined,
2025-07-12 00:24:29.149 |       socket: null,
2025-07-12 00:24:29.149 |       httpVersionMajor: 1,
2025-07-12 00:24:29.149 |       httpVersionMinor: 1,
2025-07-12 00:24:29.149 |       httpVersion: '1.1',
2025-07-12 00:24:29.149 |       complete: true,
2025-07-12 00:24:29.149 |       rawHeaders: [Array],
2025-07-12 00:24:29.149 |       rawTrailers: [],
2025-07-12 00:24:29.149 |       joinDuplicateHeaders: undefined,
2025-07-12 00:24:29.149 |       aborted: false,
2025-07-12 00:24:29.149 |       upgrade: false,
2025-07-12 00:24:29.149 |       url: '',
2025-07-12 00:24:29.149 |       method: null,
2025-07-12 00:24:29.149 |       statusCode: 413,
2025-07-12 00:24:29.149 |       statusMessage: 'Payload Too Large',
2025-07-12 00:24:29.149 |       client: [Socket],
2025-07-12 00:24:29.149 |       _consuming: false,
2025-07-12 00:24:29.149 |       _dumped: false,
2025-07-12 00:24:29.149 |       req: [Circular *1],
2025-07-12 00:24:29.149 |       _eventsCount: 4,
2025-07-12 00:24:29.149 |       responseUrl: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.149 |       redirects: [],
2025-07-12 00:24:29.149 |       [Symbol(shapeMode)]: true,
2025-07-12 00:24:29.149 |       [Symbol(kCapture)]: false,
2025-07-12 00:24:29.149 |       [Symbol(kHeaders)]: [Object],
2025-07-12 00:24:29.149 |       [Symbol(kHeadersCount)]: 16,
2025-07-12 00:24:29.149 |       [Symbol(kTrailers)]: null,
2025-07-12 00:24:29.149 |       [Symbol(kTrailersCount)]: 0
2025-07-12 00:24:29.149 |     },
2025-07-12 00:24:29.149 |     aborted: false,
2025-07-12 00:24:29.149 |     timeoutCb: null,
2025-07-12 00:24:29.149 |     upgradeOrConnect: false,
2025-07-12 00:24:29.149 |     parser: null,
2025-07-12 00:24:29.149 |     maxHeadersCount: null,
2025-07-12 00:24:29.149 |     reusedSocket: true,
2025-07-12 00:24:29.149 |     host: 'librarian',
2025-07-12 00:24:29.149 |     protocol: 'http:',
2025-07-12 00:24:29.149 |     _redirectable: Writable {
2025-07-12 00:24:29.149 |       _events: [Object],
2025-07-12 00:24:29.149 |       _writableState: [WritableState],
2025-07-12 00:24:29.149 |       _maxListeners: undefined,
2025-07-12 00:24:29.149 |       _options: [Object],
2025-07-12 00:24:29.149 |       _ended: true,
2025-07-12 00:24:29.149 |       _ending: true,
2025-07-12 00:24:29.149 |       _redirectCount: 0,
2025-07-12 00:24:29.149 |       _redirects: [],
2025-07-12 00:24:29.149 |       _requestBodyLength: 102778,
2025-07-12 00:24:29.149 |       _requestBodyBuffers: [],
2025-07-12 00:24:29.149 |       _eventsCount: 3,
2025-07-12 00:24:29.149 |       _onNativeResponse: [Function (anonymous)],
2025-07-12 00:24:29.149 |       _currentRequest: [Circular *1],
2025-07-12 00:24:29.149 |       _currentUrl: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.149 |       _timeout: null,
2025-07-12 00:24:29.149 |       [Symbol(shapeMode)]: true,
2025-07-12 00:24:29.149 |       [Symbol(kCapture)]: false
2025-07-12 00:24:29.149 |     },
2025-07-12 00:24:29.149 |     [Symbol(shapeMode)]: false,
2025-07-12 00:24:29.149 |     [Symbol(kCapture)]: false,
2025-07-12 00:24:29.149 |     [Symbol(kBytesWritten)]: 0,
2025-07-12 00:24:29.149 |     [Symbol(kNeedDrain)]: false,
2025-07-12 00:24:29.149 |     [Symbol(corked)]: 0,
2025-07-12 00:24:29.149 |     [Symbol(kOutHeaders)]: [Object: null prototype] {
2025-07-12 00:24:29.149 |       accept: [Array],
2025-07-12 00:24:29.149 |       'content-type': [Array],
2025-07-12 00:24:29.149 |       'access-control-allow-origin': [Array],
2025-07-12 00:24:29.149 |       'x-request-id': [Array],
2025-07-12 00:24:29.149 |       authorization: [Array],
2025-07-12 00:24:29.149 |       'user-agent': [Array],
2025-07-12 00:24:29.149 |       'content-length': [Array],
2025-07-12 00:24:29.149 |       'accept-encoding': [Array],
2025-07-12 00:24:29.149 |       host: [Array]
2025-07-12 00:24:29.149 |     },
2025-07-12 00:24:29.149 |     [Symbol(errored)]: null,
2025-07-12 00:24:29.149 |     [Symbol(kHighWaterMark)]: 16384,
2025-07-12 00:24:29.149 |     [Symbol(kRejectNonStandardBodyWrites)]: false,
2025-07-12 00:24:29.149 |     [Symbol(kUniqueHeaders)]: null
2025-07-12 00:24:29.149 |   },
2025-07-12 00:24:29.149 |   response: {
2025-07-12 00:24:29.149 |     status: 413,
2025-07-12 00:24:29.149 |     statusText: 'Payload Too Large',
2025-07-12 00:24:29.149 |     headers: Object [AxiosHeaders] {
2025-07-12 00:24:29.149 |       'x-powered-by': 'Express',
2025-07-12 00:24:29.149 |       'content-security-policy': "default-src 'none'",
2025-07-12 00:24:29.149 |       'x-content-type-options': 'nosniff',
2025-07-12 00:24:29.149 |       'content-type': 'text/html; charset=utf-8',
2025-07-12 00:24:29.149 |       'content-length': '144',
2025-07-12 00:24:29.149 |       date: 'Sat, 12 Jul 2025 04:24:29 GMT',
2025-07-12 00:24:29.149 |       connection: 'keep-alive',
2025-07-12 00:24:29.149 |       'keep-alive': 'timeout=5'
2025-07-12 00:24:29.149 |     },
2025-07-12 00:24:29.149 |     config: {
2025-07-12 00:24:29.149 |       transitional: [Object],
2025-07-12 00:24:29.149 |       adapter: [Array],
2025-07-12 00:24:29.149 |       transformRequest: [Array],
2025-07-12 00:24:29.149 |       transformResponse: [Array],
2025-07-12 00:24:29.149 |       timeout: 30000,
2025-07-12 00:24:29.149 |       xsrfCookieName: 'XSRF-TOKEN',
2025-07-12 00:24:29.149 |       xsrfHeaderName: 'X-XSRF-TOKEN',
2025-07-12 00:24:29.149 |       maxContentLength: -1,
2025-07-12 00:24:29.149 |       maxBodyLength: -1,
2025-07-12 00:24:29.149 |       env: [Object],
2025-07-12 00:24:29.149 |       validateStatus: [Function: validateStatus],
2025-07-12 00:24:29.149 |       headers: [Object [AxiosHeaders]],
2025-07-12 00:24:29.149 |       method: 'post',
2025-07-12 00:24:29.149 |       url: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.149 |       data: '{"id":"agent_specializations","data":[{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"agentId":"54138087-4115-44e5-af35-9f37383334cd","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T03:14:01.812Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T03:14:01.812Z"}},"1":{"agentId":"7a39664f-a97c-4014-a333-d6ab41830e60","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-26T19:36:19.624Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-26T19:36:19.624Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"a6e8c52b-ea6e-46cd-a8f8-0fdb833199a2","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T04:56:24.406Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T04:56:24.406Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"91c8bf91-3792-4310-9e11-bf62dcccf5a8","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T12:34:40.855Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T12:34:40.855Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"1a433c71-54b0-4ee8-aa22-1b125e7091d1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T18:24:07.540Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T18:24:07.540Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"e34a1868-db4f-448d-815c-0fea70b4db70","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T19:18:48.993Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T19:18:48.993Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"dff6a998-cdde-4329-8ded-7a13f8a28176","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T22:10:22.480Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T22:10:22.480Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"b1d0fe44-5c25-4a60-8696-5fefec4236e1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T17:53:23.197Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-30T17:53:23.197Z"}},"2":{"agentId":"aff51406-12b7-46ab-a1e5-a50dea02a464","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T19:26:55.855Z","performance":{"successRate":0,"'... 92778 more characters,
2025-07-12 00:24:29.149 |       allowAbsoluteUrls: true,
2025-07-12 00:24:29.149 |       _requestStartTime: 1752294268056,
2025-07-12 00:24:29.149 |       _retryCount: 1
2025-07-12 00:24:29.149 |     },
2025-07-12 00:24:29.149 |     request: <ref *1> ClientRequest {
2025-07-12 00:24:29.149 |       _events: [Object: null prototype],
2025-07-12 00:24:29.149 |       _eventsCount: 7,
2025-07-12 00:24:29.149 |       _maxListeners: undefined,
2025-07-12 00:24:29.149 |       outputData: [],
2025-07-12 00:24:29.149 |       outputSize: 0,
2025-07-12 00:24:29.149 |       writable: true,
2025-07-12 00:24:29.149 |       destroyed: true,
2025-07-12 00:24:29.149 |       _last: false,
2025-07-12 00:24:29.149 |       chunkedEncoding: false,
2025-07-12 00:24:29.149 |       shouldKeepAlive: true,
2025-07-12 00:24:29.149 |       maxRequestsOnConnectionReached: false,
2025-07-12 00:24:29.149 |       _defaultKeepAlive: true,
2025-07-12 00:24:29.149 |       useChunkedEncodingByDefault: true,
2025-07-12 00:24:29.149 |       sendDate: false,
2025-07-12 00:24:29.149 |       _removedConnection: false,
2025-07-12 00:24:29.149 |       _removedContLen: false,
2025-07-12 00:24:29.149 |       _removedTE: false,
2025-07-12 00:24:29.149 |       strictContentLength: false,
2025-07-12 00:24:29.149 |       _contentLength: '102778',
2025-07-12 00:24:29.149 |       _hasBody: true,
2025-07-12 00:24:29.149 |       _trailer: '',
2025-07-12 00:24:29.149 |       finished: true,
2025-07-12 00:24:29.149 |       _headerSent: true,
2025-07-12 00:24:29.149 |       _closed: true,
2025-07-12 00:24:29.149 |       socket: [Socket],
2025-07-12 00:24:29.149 |       _header: 'POST /storeData HTTP/1.1\r\n' +
2025-07-12 00:24:29.149 |         'Accept: application/json, text/plain, */*\r\n' +
2025-07-12 00:24:29.149 |         'Content-Type: application/json\r\n' +
2025-07-12 00:24:29.149 |         'Access-Control-Allow-Origin: *\r\n' +
2025-07-12 00:24:29.149 |         'X-Request-ID: n7xvduuytz9\r\n' +
2025-07-12 00:24:29.149 |         'Authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n' +
2025-07-12 00:24:29.149 |         'User-Agent: axios/1.9.0\r\n' +
2025-07-12 00:24:29.149 |         'Content-Length: 102778\r\n' +
2025-07-12 00:24:29.149 |         'Accept-Encoding: gzip, compress, deflate, br\r\n' +
2025-07-12 00:24:29.149 |         'Host: librarian:5040\r\n' +
2025-07-12 00:24:29.149 |         'Connection: keep-alive\r\n' +
2025-07-12 00:24:29.149 |         '\r\n',
2025-07-12 00:24:29.149 |       _keepAliveTimeout: 0,
2025-07-12 00:24:29.150 |       _onPendingData: [Function: nop],
2025-07-12 00:24:29.150 |       agent: [Agent],
2025-07-12 00:24:29.150 |       socketPath: undefined,
2025-07-12 00:24:29.150 |       method: 'POST',
2025-07-12 00:24:29.150 |       maxHeaderSize: undefined,
2025-07-12 00:24:29.150 |       insecureHTTPParser: undefined,
2025-07-12 00:24:29.150 |       joinDuplicateHeaders: undefined,
2025-07-12 00:24:29.150 |       path: '/storeData',
2025-07-12 00:24:29.150 |       _ended: true,
2025-07-12 00:24:29.150 |       res: [IncomingMessage],
2025-07-12 00:24:29.150 |       aborted: false,
2025-07-12 00:24:29.150 |       timeoutCb: null,
2025-07-12 00:24:29.150 |       upgradeOrConnect: false,
2025-07-12 00:24:29.150 |       parser: null,
2025-07-12 00:24:29.150 |       maxHeadersCount: null,
2025-07-12 00:24:29.150 |       reusedSocket: true,
2025-07-12 00:24:29.150 |       host: 'librarian',
2025-07-12 00:24:29.150 |       protocol: 'http:',
2025-07-12 00:24:29.150 |       _redirectable: [Writable],
2025-07-12 00:24:29.150 |       [Symbol(shapeMode)]: false,
2025-07-12 00:24:29.150 |       [Symbol(kCapture)]: false,
2025-07-12 00:24:29.150 |       [Symbol(kBytesWritten)]: 0,
2025-07-12 00:24:29.150 |       [Symbol(kNeedDrain)]: false,
2025-07-12 00:24:29.150 |       [Symbol(corked)]: 0,
2025-07-12 00:24:29.150 |       [Symbol(kOutHeaders)]: [Object: null prototype],
2025-07-12 00:24:29.150 |       [Symbol(errored)]: null,
2025-07-12 00:24:29.150 |       [Symbol(kHighWaterMark)]: 16384,
2025-07-12 00:24:29.150 |       [Symbol(kRejectNonStandardBodyWrites)]: false,
2025-07-12 00:24:29.150 |       [Symbol(kUniqueHeaders)]: null
2025-07-12 00:24:29.150 |     },
2025-07-12 00:24:29.150 |     data: '<!DOCTYPE html>\n' +
2025-07-12 00:24:29.150 |       '<html lang="en">\n' +
2025-07-12 00:24:29.150 |       '<head>\n' +
2025-07-12 00:24:29.150 |       '<meta charset="utf-8">\n' +
2025-07-12 00:24:29.150 |       '<title>Error</title>\n' +
2025-07-12 00:24:29.150 |       '</head>\n' +
2025-07-12 00:24:29.150 |       '<body>\n' +
2025-07-12 00:24:29.150 |       '<pre>Payload Too Large</pre>\n' +
2025-07-12 00:24:29.150 |       '</body>\n' +
2025-07-12 00:24:29.150 |       '</html>\n'
2025-07-12 00:24:29.150 |   },
2025-07-12 00:24:29.150 |   status: 413
2025-07-12 00:24:29.150 | }
2025-07-12 00:24:29.160 | Found agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with role creative
2025-07-12 00:24:29.173 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 received collaboration message: {
2025-07-12 00:24:29.173 |   id: 'a3fcc726-6cfd-4cd6-9871-45b7a4531505',
2025-07-12 00:24:29.173 |   type: 'task_delegation',
2025-07-12 00:24:29.173 |   senderId: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.173 |   recipientId: 'b31d7887-fc71-4b2b-b1a9-5206e575be13',
2025-07-12 00:24:29.173 |   payload: {
2025-07-12 00:24:29.173 |     id: '3dd538d8-d675-4953-9c63-08659aa6d38e',
2025-07-12 00:24:29.173 |     type: 'THINK',
2025-07-12 00:24:29.173 |     description: 'Create customized resumes and cover letters for each job posting',
2025-07-12 00:24:29.173 |     inputs: undefined,
2025-07-12 00:24:29.173 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.173 |     delegatedTo: 'b31d7887-fc71-4b2b-b1a9-5206e575be13',
2025-07-12 00:24:29.173 |     status: 'pending',
2025-07-12 00:24:29.173 |     createdAt: '2025-07-12T04:24:29.172Z',
2025-07-12 00:24:29.173 |     updatedAt: '2025-07-12T04:24:29.172Z',
2025-07-12 00:24:29.173 |     deadline: undefined,
2025-07-12 00:24:29.173 |     priority: 'normal'
2025-07-12 00:24:29.173 |   },
2025-07-12 00:24:29.173 |   content: {
2025-07-12 00:24:29.173 |     id: '3dd538d8-d675-4953-9c63-08659aa6d38e',
2025-07-12 00:24:29.173 |     type: 'THINK',
2025-07-12 00:24:29.173 |     description: 'Create customized resumes and cover letters for each job posting',
2025-07-12 00:24:29.173 |     inputs: undefined,
2025-07-12 00:24:29.173 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.173 |     delegatedTo: 'b31d7887-fc71-4b2b-b1a9-5206e575be13',
2025-07-12 00:24:29.173 |     status: 'pending',
2025-07-12 00:24:29.173 |     createdAt: '2025-07-12T04:24:29.172Z',
2025-07-12 00:24:29.173 |     updatedAt: '2025-07-12T04:24:29.172Z',
2025-07-12 00:24:29.173 |     deadline: undefined,
2025-07-12 00:24:29.173 |     priority: 'normal'
2025-07-12 00:24:29.173 |   },
2025-07-12 00:24:29.173 |   timestamp: '2025-07-12T04:24:29.173Z'
2025-07-12 00:24:29.173 | }
2025-07-12 00:24:29.186 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:29.189 | Successfully delegated step 13bd4346-ef11-4445-b70d-9cf23e4c1a6f to agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 00:24:29.203 | Binding queue to exchange: stage7
2025-07-12 00:24:29.215 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 13bd4346-ef11-4445-b70d-9cf23e4c1a6f
2025-07-12 00:24:29.261 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:29.261 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:29.261 | Executing step THINK (17b428b5-91b6-40c2-88ab-71913e1b4d7f)...
2025-07-12 00:24:29.261 | Step 17b428b5-91b6-40c2-88ab-71913e1b4d7f recommends role researcher, but this agent has role executor
2025-07-12 00:24:29.261 | Attempting to delegate step 17b428b5-91b6-40c2-88ab-71913e1b4d7f to an agent with role researcher
2025-07-12 00:24:29.282 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:29.297 | Found agent c66266ad-88ed-4844-8929-78db18015b49 with role researcher
2025-07-12 00:24:29.339 | Agent c66266ad-88ed-4844-8929-78db18015b49 received collaboration message: {
2025-07-12 00:24:29.339 |   id: '5e020a21-ce87-451d-983f-85c7536010f4',
2025-07-12 00:24:29.339 |   type: 'task_delegation',
2025-07-12 00:24:29.339 |   senderId: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.339 |   recipientId: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:29.339 |   payload: {
2025-07-12 00:24:29.339 |     id: 'a0335cd4-3859-4f6f-8f84-3610d865df0c',
2025-07-12 00:24:29.339 |     type: 'THINK',
2025-07-12 00:24:29.339 |     description: 'Develop a plan to continuously monitor the internet for future job posts',
2025-07-12 00:24:29.339 |     inputs: undefined,
2025-07-12 00:24:29.339 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.339 |     delegatedTo: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:29.339 |     status: 'pending',
2025-07-12 00:24:29.339 |     createdAt: '2025-07-12T04:24:29.338Z',
2025-07-12 00:24:29.339 |     updatedAt: '2025-07-12T04:24:29.338Z',
2025-07-12 00:24:29.339 |     deadline: undefined,
2025-07-12 00:24:29.339 |     priority: 'normal'
2025-07-12 00:24:29.339 |   },
2025-07-12 00:24:29.339 |   content: {
2025-07-12 00:24:29.339 |     id: 'a0335cd4-3859-4f6f-8f84-3610d865df0c',
2025-07-12 00:24:29.339 |     type: 'THINK',
2025-07-12 00:24:29.339 |     description: 'Develop a plan to continuously monitor the internet for future job posts',
2025-07-12 00:24:29.339 |     inputs: undefined,
2025-07-12 00:24:29.339 |     delegatedBy: 'ded32008-8dc5-49e7-bdc9-d32acba1c461',
2025-07-12 00:24:29.339 |     delegatedTo: 'c66266ad-88ed-4844-8929-78db18015b49',
2025-07-12 00:24:29.339 |     status: 'pending',
2025-07-12 00:24:29.339 |     createdAt: '2025-07-12T04:24:29.338Z',
2025-07-12 00:24:29.339 |     updatedAt: '2025-07-12T04:24:29.338Z',
2025-07-12 00:24:29.339 |     deadline: undefined,
2025-07-12 00:24:29.339 |     priority: 'normal'
2025-07-12 00:24:29.339 |   },
2025-07-12 00:24:29.340 |   timestamp: '2025-07-12T04:24:29.338Z'
2025-07-12 00:24:29.340 | }
2025-07-12 00:24:29.352 | Successfully delegated step 17b428b5-91b6-40c2-88ab-71913e1b4d7f to agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:24:29.368 | Saving work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 17b428b5-91b6-40c2-88ab-71913e1b4d7f
2025-07-12 00:24:29.383 | AgentSet received update from agent c66266ad-88ed-4844-8929-78db18015b49 with status running
2025-07-12 00:24:29.411 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:29.412 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type workProductUpdate to user
2025-07-12 00:24:29.428 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-12 00:24:29.428 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:29.442 | Loading work product for agent ded32008-8dc5-49e7-bdc9-d32acba1c461, step 17b428b5-91b6-40c2-88ab-71913e1b4d7f
2025-07-12 00:24:29.498 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:29.501 | Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 has completed its work.
2025-07-12 00:24:29.501 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 has completed its work.
2025-07-12 00:24:29.511 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:29.511 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 saying: Result: {"agentId":"ded32008-8dc5-49e7-bdc9-d32acba1c461","stepId":"17b428b5-91b6-40c2-88ab-71913e1b4d7f","data":{"_id":"ded32008-8dc5-49e7-bdc9-d32acba1c461_17b428b5-91b6-40c2-88ab-71913e1b4d7f","agentId":"ded32008-8dc5-49e7-bdc9-d32acba1c461","data":[{"success":true,"name":"delegation","resultType":"object","resultDescription":"Step delegated to specialized agent","result":{"taskId":"a0335cd4-3859-4f6f-8f84-3610d865df0c","recipientId":"c66266ad-88ed-4844-8929-78db18015b49","estimatedCompletion":"2025-07-12T04:29:29.339Z"}}],"id":"ded32008-8dc5-49e7-bdc9-d32acba1c461_17b428b5-91b6-40c2-88ab-71913e1b4d7f","stepId":"17b428b5-91b6-40c2-88ab-71913e1b4d7f","timestamp":"2025-07-12T04:24:29.371Z"}}
2025-07-12 00:24:29.512 | AgentSet ded32008-8dc5-49e7-bdc9-d32acba1c461 sending message of type say to user
2025-07-12 00:24:29.568 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:29.568 | Successfully sent message to PostOffice: Agent ded32008-8dc5-49e7-bdc9-d32acba1c461 has completed its work.
2025-07-12 00:24:29.569 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:29.569 | Successfully sent message to PostOffice: Result: {"agentId":"ded32008-8dc5-49e7-bdc9-d32acba1c461","stepId":"17b428b5-91b6-40c2-88ab-71913e1b4d7f","data":{"_id":"ded32008-8dc5-49e7-bdc9-d32acba1c461_17b428b5-91b6-40c2-88ab-71913e1b4d7f","agentId":"ded32008-8dc5-49e7-bdc9-d32acba1c461","data":[{"success":true,"name":"delegation","resultType":"object","resultDescription":"Step delegated to specialized agent","result":{"taskId":"a0335cd4-3859-4f6f-8f84-3610d865df0c","recipientId":"c66266ad-88ed-4844-8929-78db18015b49","estimatedCompletion":"2025-07-12T04:29:29.339Z"}}],"id":"ded32008-8dc5-49e7-bdc9-d32acba1c461_17b428b5-91b6-40c2-88ab-71913e1b4d7f","stepId":"17b428b5-91b6-40c2-88ab-71913e1b4d7f","timestamp":"2025-07-12T04:24:29.371Z"}}
2025-07-12 00:24:29.704 | Error already analyzed: AxiosError:Request failed with status code 413
2025-07-12 00:24:29.704 | Applied role Creative to agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 00:24:29.704 | Assigned role creative to agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 00:24:29.704 | Starting agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 00:24:29.704 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 notifying TrafficManager of status: running
2025-07-12 00:24:29.704 | Error saving agent specializations: AxiosError: Request failed with status code 413
2025-07-12 00:24:29.704 |     at settle (/usr/src/app/node_modules/axios/dist/node/axios.cjs:2049:12)
2025-07-12 00:24:29.704 |     at IncomingMessage.handleStreamEnd (/usr/src/app/node_modules/axios/dist/node/axios.cjs:3166:11)
2025-07-12 00:24:29.704 |     at IncomingMessage.emit (node:events:536:35)
2025-07-12 00:24:29.704 |     at endReadableNT (node:internal/streams/readable:1698:12)
2025-07-12 00:24:29.704 |     at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
2025-07-12 00:24:29.704 |     at Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4276:41)
2025-07-12 00:24:29.704 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12 00:24:29.704 |     at async Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4271:14)
2025-07-12 00:24:29.705 |     at async SpecializationFramework.saveSpecializations (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:67:13)
2025-07-12 00:24:29.705 |     at async SpecializationFramework.assignRole (/usr/src/app/services/agentset/dist/specialization/SpecializationFramework.js:156:9)
2025-07-12 00:24:29.705 |     at async AgentSet.addAgentWithConfig (/usr/src/app/services/agentset/dist/AgentSet.js:857:21) {
2025-07-12 00:24:29.705 |   code: 'ERR_BAD_REQUEST',
2025-07-12 00:24:29.705 |   config: {
2025-07-12 00:24:29.705 |     transitional: {
2025-07-12 00:24:29.705 |       silentJSONParsing: true,
2025-07-12 00:24:29.705 |       forcedJSONParsing: true,
2025-07-12 00:24:29.705 |       clarifyTimeoutError: false
2025-07-12 00:24:29.705 |     },
2025-07-12 00:24:29.705 |     adapter: [ 'xhr', 'http', 'fetch' ],
2025-07-12 00:24:29.705 |     transformRequest: [ [Function: transformRequest] ],
2025-07-12 00:24:29.705 |     transformResponse: [ [Function: transformResponse] ],
2025-07-12 00:24:29.705 |     timeout: 30000,
2025-07-12 00:24:29.705 |     xsrfCookieName: 'XSRF-TOKEN',
2025-07-12 00:24:29.705 |     xsrfHeaderName: 'X-XSRF-TOKEN',
2025-07-12 00:24:29.705 |     maxContentLength: -1,
2025-07-12 00:24:29.705 |     maxBodyLength: -1,
2025-07-12 00:24:29.705 |     env: { FormData: [Function [FormData]], Blob: [class Blob] },
2025-07-12 00:24:29.705 |     validateStatus: [Function: validateStatus],
2025-07-12 00:24:29.705 |     headers: Object [AxiosHeaders] {
2025-07-12 00:24:29.705 |       Accept: 'application/json, text/plain, */*',
2025-07-12 00:24:29.705 |       'Content-Type': 'application/json',
2025-07-12 00:24:29.705 |       'Access-Control-Allow-Origin': '*',
2025-07-12 00:24:29.705 |       'X-Request-ID': '6bio3qiw396',
2025-07-12 00:24:29.705 |       Authorization: 'Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
2025-07-12 00:24:29.705 |       'User-Agent': 'axios/1.9.0',
2025-07-12 00:24:29.705 |       'Content-Length': '103020',
2025-07-12 00:24:29.705 |       'Accept-Encoding': 'gzip, compress, deflate, br'
2025-07-12 00:24:29.705 |     },
2025-07-12 00:24:29.705 |     method: 'post',
2025-07-12 00:24:29.705 |     url: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.705 |     data: '{"id":"agent_specializations","data":[{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"agentId":"54138087-4115-44e5-af35-9f37383334cd","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T03:14:01.812Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T03:14:01.812Z"}},"1":{"agentId":"7a39664f-a97c-4014-a333-d6ab41830e60","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-26T19:36:19.624Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-26T19:36:19.624Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"a6e8c52b-ea6e-46cd-a8f8-0fdb833199a2","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T04:56:24.406Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T04:56:24.406Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"91c8bf91-3792-4310-9e11-bf62dcccf5a8","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T12:34:40.855Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T12:34:40.855Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"1a433c71-54b0-4ee8-aa22-1b125e7091d1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T18:24:07.540Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T18:24:07.540Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"e34a1868-db4f-448d-815c-0fea70b4db70","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T19:18:48.993Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T19:18:48.993Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"dff6a998-cdde-4329-8ded-7a13f8a28176","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T22:10:22.480Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T22:10:22.480Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"b1d0fe44-5c25-4a60-8696-5fefec4236e1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T17:53:23.197Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-30T17:53:23.197Z"}},"2":{"agentId":"aff51406-12b7-46ab-a1e5-a50dea02a464","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T19:26:55.855Z","performance":{"successRate":0,"'... 93020 more characters,
2025-07-12 00:24:29.705 |     allowAbsoluteUrls: true,
2025-07-12 00:24:29.705 |     _requestStartTime: 1752294268552,
2025-07-12 00:24:29.705 |     _retryCount: 1
2025-07-12 00:24:29.705 |   },
2025-07-12 00:24:29.705 |   request: <ref *1> ClientRequest {
2025-07-12 00:24:29.705 |     _events: [Object: null prototype] {
2025-07-12 00:24:29.705 |       abort: [Function (anonymous)],
2025-07-12 00:24:29.705 |       aborted: [Function (anonymous)],
2025-07-12 00:24:29.705 |       connect: [Function (anonymous)],
2025-07-12 00:24:29.705 |       error: [Function (anonymous)],
2025-07-12 00:24:29.705 |       socket: [Function (anonymous)],
2025-07-12 00:24:29.705 |       timeout: [Function (anonymous)],
2025-07-12 00:24:29.705 |       finish: [Function: requestOnFinish]
2025-07-12 00:24:29.705 |     },
2025-07-12 00:24:29.705 |     _eventsCount: 7,
2025-07-12 00:24:29.705 |     _maxListeners: undefined,
2025-07-12 00:24:29.705 |     outputData: [],
2025-07-12 00:24:29.705 |     outputSize: 0,
2025-07-12 00:24:29.705 |     writable: true,
2025-07-12 00:24:29.705 |     destroyed: true,
2025-07-12 00:24:29.705 |     _last: false,
2025-07-12 00:24:29.705 |     chunkedEncoding: false,
2025-07-12 00:24:29.705 |     shouldKeepAlive: true,
2025-07-12 00:24:29.705 |     maxRequestsOnConnectionReached: false,
2025-07-12 00:24:29.705 |     _defaultKeepAlive: true,
2025-07-12 00:24:29.705 |     useChunkedEncodingByDefault: true,
2025-07-12 00:24:29.705 |     sendDate: false,
2025-07-12 00:24:29.705 |     _removedConnection: false,
2025-07-12 00:24:29.705 |     _removedContLen: false,
2025-07-12 00:24:29.705 |     _removedTE: false,
2025-07-12 00:24:29.705 |     strictContentLength: false,
2025-07-12 00:24:29.705 |     _contentLength: '103020',
2025-07-12 00:24:29.705 |     _hasBody: true,
2025-07-12 00:24:29.705 |     _trailer: '',
2025-07-12 00:24:29.705 |     finished: true,
2025-07-12 00:24:29.705 |     _headerSent: true,
2025-07-12 00:24:29.705 |     _closed: true,
2025-07-12 00:24:29.705 |     socket: Socket {
2025-07-12 00:24:29.705 |       connecting: false,
2025-07-12 00:24:29.705 |       _hadError: false,
2025-07-12 00:24:29.705 |       _parent: null,
2025-07-12 00:24:29.705 |       _host: 'librarian',
2025-07-12 00:24:29.705 |       _closeAfterHandlingError: false,
2025-07-12 00:24:29.705 |       _events: [Object],
2025-07-12 00:24:29.705 |       _readableState: [ReadableState],
2025-07-12 00:24:29.705 |       _writableState: [WritableState],
2025-07-12 00:24:29.705 |       allowHalfOpen: false,
2025-07-12 00:24:29.705 |       _maxListeners: undefined,
2025-07-12 00:24:29.705 |       _eventsCount: 6,
2025-07-12 00:24:29.705 |       _sockname: null,
2025-07-12 00:24:29.705 |       _pendingData: null,
2025-07-12 00:24:29.705 |       _pendingEncoding: '',
2025-07-12 00:24:29.705 |       server: null,
2025-07-12 00:24:29.705 |       _server: null,
2025-07-12 00:24:29.705 |       timeout: 4000,
2025-07-12 00:24:29.705 |       parser: null,
2025-07-12 00:24:29.705 |       _httpMessage: null,
2025-07-12 00:24:29.705 |       [Symbol(async_id_symbol)]: -1,
2025-07-12 00:24:29.705 |       [Symbol(kHandle)]: [TCP],
2025-07-12 00:24:29.705 |       [Symbol(lastWriteQueueSize)]: 0,
2025-07-12 00:24:29.705 |       [Symbol(timeout)]: Timeout {
2025-07-12 00:24:29.705 |         _idleTimeout: 4000,
2025-07-12 00:24:29.705 |         _idlePrev: [TimersList],
2025-07-12 00:24:29.705 |         _idleNext: [Timeout],
2025-07-12 00:24:29.705 |         _idleStart: 3118208,
2025-07-12 00:24:29.705 |         _onTimeout: [Function: bound ],
2025-07-12 00:24:29.705 |         _timerArgs: undefined,
2025-07-12 00:24:29.705 |         _repeat: null,
2025-07-12 00:24:29.705 |         _destroyed: false,
2025-07-12 00:24:29.705 |         [Symbol(refed)]: false,
2025-07-12 00:24:29.705 |         [Symbol(kHasPrimitive)]: false,
2025-07-12 00:24:29.705 |         [Symbol(asyncId)]: 208718,
2025-07-12 00:24:29.705 |         [Symbol(triggerId)]: 208716
2025-07-12 00:24:29.705 |       },
2025-07-12 00:24:29.705 |       [Symbol(kBuffer)]: null,
2025-07-12 00:24:29.705 |       [Symbol(kBufferCb)]: null,
2025-07-12 00:24:29.705 |       [Symbol(kBufferGen)]: null,
2025-07-12 00:24:29.705 |       [Symbol(shapeMode)]: true,
2025-07-12 00:24:29.705 |       [Symbol(kCapture)]: false,
2025-07-12 00:24:29.705 |       [Symbol(kSetNoDelay)]: true,
2025-07-12 00:24:29.705 |       [Symbol(kSetKeepAlive)]: true,
2025-07-12 00:24:29.705 |       [Symbol(kSetKeepAliveInitialDelay)]: 1,
2025-07-12 00:24:29.705 |       [Symbol(kBytesRead)]: 0,
2025-07-12 00:24:29.705 |       [Symbol(kBytesWritten)]: 0
2025-07-12 00:24:29.705 |     },
2025-07-12 00:24:29.705 |     _header: 'POST /storeData HTTP/1.1\r\n' +
2025-07-12 00:24:29.705 |       'Accept: application/json, text/plain, */*\r\n' +
2025-07-12 00:24:29.705 |       'Content-Type: application/json\r\n' +
2025-07-12 00:24:29.705 |       'Access-Control-Allow-Origin: *\r\n' +
2025-07-12 00:24:29.705 |       'X-Request-ID: 6bio3qiw396\r\n' +
2025-07-12 00:24:29.705 |       'Authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n' +
2025-07-12 00:24:29.705 |       'User-Agent: axios/1.9.0\r\n' +
2025-07-12 00:24:29.705 |       'Content-Length: 103020\r\n' +
2025-07-12 00:24:29.706 |       'Accept-Encoding: gzip, compress, deflate, br\r\n' +
2025-07-12 00:24:29.706 |       'Host: librarian:5040\r\n' +
2025-07-12 00:24:29.706 |       'Connection: keep-alive\r\n' +
2025-07-12 00:24:29.706 |       '\r\n',
2025-07-12 00:24:29.706 |     _keepAliveTimeout: 0,
2025-07-12 00:24:29.706 |     _onPendingData: [Function: nop],
2025-07-12 00:24:29.706 |     agent: Agent {
2025-07-12 00:24:29.706 |       _events: [Object: null prototype],
2025-07-12 00:24:29.706 |       _eventsCount: 2,
2025-07-12 00:24:29.706 |       _maxListeners: undefined,
2025-07-12 00:24:29.706 |       defaultPort: 80,
2025-07-12 00:24:29.706 |       protocol: 'http:',
2025-07-12 00:24:29.706 |       options: [Object: null prototype],
2025-07-12 00:24:29.706 |       requests: [Object: null prototype] {},
2025-07-12 00:24:29.706 |       sockets: [Object: null prototype],
2025-07-12 00:24:29.706 |       freeSockets: [Object: null prototype],
2025-07-12 00:24:29.706 |       keepAliveMsecs: 1000,
2025-07-12 00:24:29.706 |       keepAlive: true,
2025-07-12 00:24:29.706 |       maxSockets: Infinity,
2025-07-12 00:24:29.706 |       maxFreeSockets: 256,
2025-07-12 00:24:29.706 |       scheduling: 'lifo',
2025-07-12 00:24:29.706 |       maxTotalSockets: Infinity,
2025-07-12 00:24:29.706 |       totalSocketCount: 22,
2025-07-12 00:24:29.706 |       [Symbol(shapeMode)]: false,
2025-07-12 00:24:29.706 |       [Symbol(kCapture)]: false
2025-07-12 00:24:29.706 |     },
2025-07-12 00:24:29.706 |     socketPath: undefined,
2025-07-12 00:24:29.706 |     method: 'POST',
2025-07-12 00:24:29.706 |     maxHeaderSize: undefined,
2025-07-12 00:24:29.706 |     insecureHTTPParser: undefined,
2025-07-12 00:24:29.706 |     joinDuplicateHeaders: undefined,
2025-07-12 00:24:29.706 |     path: '/storeData',
2025-07-12 00:24:29.706 |     _ended: true,
2025-07-12 00:24:29.706 |     res: IncomingMessage {
2025-07-12 00:24:29.706 |       _events: [Object],
2025-07-12 00:24:29.706 |       _readableState: [ReadableState],
2025-07-12 00:24:29.706 |       _maxListeners: undefined,
2025-07-12 00:24:29.706 |       socket: null,
2025-07-12 00:24:29.706 |       httpVersionMajor: 1,
2025-07-12 00:24:29.706 |       httpVersionMinor: 1,
2025-07-12 00:24:29.706 |       httpVersion: '1.1',
2025-07-12 00:24:29.706 |       complete: true,
2025-07-12 00:24:29.706 |       rawHeaders: [Array],
2025-07-12 00:24:29.706 |       rawTrailers: [],
2025-07-12 00:24:29.706 |       joinDuplicateHeaders: undefined,
2025-07-12 00:24:29.706 |       aborted: false,
2025-07-12 00:24:29.706 |       upgrade: false,
2025-07-12 00:24:29.706 |       url: '',
2025-07-12 00:24:29.706 |       method: null,
2025-07-12 00:24:29.706 |       statusCode: 413,
2025-07-12 00:24:29.706 |       statusMessage: 'Payload Too Large',
2025-07-12 00:24:29.706 |       client: [Socket],
2025-07-12 00:24:29.706 |       _consuming: false,
2025-07-12 00:24:29.706 |       _dumped: false,
2025-07-12 00:24:29.706 |       req: [Circular *1],
2025-07-12 00:24:29.706 |       _eventsCount: 4,
2025-07-12 00:24:29.706 |       responseUrl: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.706 |       redirects: [],
2025-07-12 00:24:29.706 |       [Symbol(shapeMode)]: true,
2025-07-12 00:24:29.706 |       [Symbol(kCapture)]: false,
2025-07-12 00:24:29.706 |       [Symbol(kHeaders)]: [Object],
2025-07-12 00:24:29.706 |       [Symbol(kHeadersCount)]: 16,
2025-07-12 00:24:29.706 |       [Symbol(kTrailers)]: null,
2025-07-12 00:24:29.706 |       [Symbol(kTrailersCount)]: 0
2025-07-12 00:24:29.706 |     },
2025-07-12 00:24:29.706 |     aborted: false,
2025-07-12 00:24:29.706 |     timeoutCb: null,
2025-07-12 00:24:29.706 |     upgradeOrConnect: false,
2025-07-12 00:24:29.706 |     parser: null,
2025-07-12 00:24:29.706 |     maxHeadersCount: null,
2025-07-12 00:24:29.706 |     reusedSocket: true,
2025-07-12 00:24:29.706 |     host: 'librarian',
2025-07-12 00:24:29.706 |     protocol: 'http:',
2025-07-12 00:24:29.706 |     _redirectable: Writable {
2025-07-12 00:24:29.706 |       _events: [Object],
2025-07-12 00:24:29.706 |       _writableState: [WritableState],
2025-07-12 00:24:29.706 |       _maxListeners: undefined,
2025-07-12 00:24:29.706 |       _options: [Object],
2025-07-12 00:24:29.706 |       _ended: true,
2025-07-12 00:24:29.706 |       _ending: true,
2025-07-12 00:24:29.706 |       _redirectCount: 0,
2025-07-12 00:24:29.706 |       _redirects: [],
2025-07-12 00:24:29.706 |       _requestBodyLength: 103020,
2025-07-12 00:24:29.706 |       _requestBodyBuffers: [],
2025-07-12 00:24:29.706 |       _eventsCount: 3,
2025-07-12 00:24:29.706 |       _onNativeResponse: [Function (anonymous)],
2025-07-12 00:24:29.706 |       _currentRequest: [Circular *1],
2025-07-12 00:24:29.706 |       _currentUrl: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.706 |       _timeout: null,
2025-07-12 00:24:29.706 |       [Symbol(shapeMode)]: true,
2025-07-12 00:24:29.706 |       [Symbol(kCapture)]: false
2025-07-12 00:24:29.706 |     },
2025-07-12 00:24:29.706 |     [Symbol(shapeMode)]: false,
2025-07-12 00:24:29.706 |     [Symbol(kCapture)]: false,
2025-07-12 00:24:29.706 |     [Symbol(kBytesWritten)]: 0,
2025-07-12 00:24:29.706 |     [Symbol(kNeedDrain)]: false,
2025-07-12 00:24:29.706 |     [Symbol(corked)]: 0,
2025-07-12 00:24:29.706 |     [Symbol(kOutHeaders)]: [Object: null prototype] {
2025-07-12 00:24:29.706 |       accept: [Array],
2025-07-12 00:24:29.706 |       'content-type': [Array],
2025-07-12 00:24:29.706 |       'access-control-allow-origin': [Array],
2025-07-12 00:24:29.706 |       'x-request-id': [Array],
2025-07-12 00:24:29.706 |       authorization: [Array],
2025-07-12 00:24:29.706 |       'user-agent': [Array],
2025-07-12 00:24:29.706 |       'content-length': [Array],
2025-07-12 00:24:29.706 |       'accept-encoding': [Array],
2025-07-12 00:24:29.706 |       host: [Array]
2025-07-12 00:24:29.706 |     },
2025-07-12 00:24:29.706 |     [Symbol(errored)]: null,
2025-07-12 00:24:29.706 |     [Symbol(kHighWaterMark)]: 16384,
2025-07-12 00:24:29.706 |     [Symbol(kRejectNonStandardBodyWrites)]: false,
2025-07-12 00:24:29.706 |     [Symbol(kUniqueHeaders)]: null
2025-07-12 00:24:29.706 |   },
2025-07-12 00:24:29.706 |   response: {
2025-07-12 00:24:29.706 |     status: 413,
2025-07-12 00:24:29.706 |     statusText: 'Payload Too Large',
2025-07-12 00:24:29.706 |     headers: Object [AxiosHeaders] {
2025-07-12 00:24:29.706 |       'x-powered-by': 'Express',
2025-07-12 00:24:29.706 |       'content-security-policy': "default-src 'none'",
2025-07-12 00:24:29.706 |       'x-content-type-options': 'nosniff',
2025-07-12 00:24:29.706 |       'content-type': 'text/html; charset=utf-8',
2025-07-12 00:24:29.706 |       'content-length': '144',
2025-07-12 00:24:29.706 |       date: 'Sat, 12 Jul 2025 04:24:29 GMT',
2025-07-12 00:24:29.706 |       connection: 'keep-alive',
2025-07-12 00:24:29.706 |       'keep-alive': 'timeout=5'
2025-07-12 00:24:29.706 |     },
2025-07-12 00:24:29.706 |     config: {
2025-07-12 00:24:29.706 |       transitional: [Object],
2025-07-12 00:24:29.706 |       adapter: [Array],
2025-07-12 00:24:29.706 |       transformRequest: [Array],
2025-07-12 00:24:29.706 |       transformResponse: [Array],
2025-07-12 00:24:29.706 |       timeout: 30000,
2025-07-12 00:24:29.706 |       xsrfCookieName: 'XSRF-TOKEN',
2025-07-12 00:24:29.706 |       xsrfHeaderName: 'X-XSRF-TOKEN',
2025-07-12 00:24:29.706 |       maxContentLength: -1,
2025-07-12 00:24:29.706 |       maxBodyLength: -1,
2025-07-12 00:24:29.706 |       env: [Object],
2025-07-12 00:24:29.706 |       validateStatus: [Function: validateStatus],
2025-07-12 00:24:29.706 |       headers: [Object [AxiosHeaders]],
2025-07-12 00:24:29.706 |       method: 'post',
2025-07-12 00:24:29.706 |       url: 'http://librarian:5040/storeData',
2025-07-12 00:24:29.706 |       data: '{"id":"agent_specializations","data":[{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"0":{"agentId":"54138087-4115-44e5-af35-9f37383334cd","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T03:14:01.812Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T03:14:01.812Z"}},"1":{"agentId":"7a39664f-a97c-4014-a333-d6ab41830e60","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-26T19:36:19.624Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-26T19:36:19.624Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"a6e8c52b-ea6e-46cd-a8f8-0fdb833199a2","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T04:56:24.406Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T04:56:24.406Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"91c8bf91-3792-4310-9e11-bf62dcccf5a8","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T12:34:40.855Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T12:34:40.855Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"1a433c71-54b0-4ee8-aa22-1b125e7091d1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T18:24:07.540Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T18:24:07.540Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"e34a1868-db4f-448d-815c-0fea70b4db70","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T19:18:48.993Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T19:18:48.993Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"dff6a998-cdde-4329-8ded-7a13f8a28176","roleId":"executor","proficiency":50,"assignedAt":"2025-06-27T22:10:22.480Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-27T22:10:22.480Z"}},"2":{"agentId":"cd45b5e6-f329-4abb-b2c3-4c08f04da9e5","roleId":"researcher","proficiency":50,"assignedAt":"2025-06-25T19:52:49.443Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-25T19:52:49.443Z"}},"3":{"agentId":"d79ffb62-ef6c-4596-aeb4-8fd3f53411bb","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:13.127Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:13.127Z"}},"4":{"agentId":"0a61cab6-b13f-4045-8ca9-d6d2450ef0ba","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:29:59.694Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:29:59.694Z"}},"5":{"agentId":"81a34eea-29a8-4601-ac2c-f735e61e5712","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:30:10.997Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:30:10.997Z"}},"6":{"agentId":"4abc01ea-9dce-4a5b-ac1b-e3aced226899","roleId":"executor","proficiency":50,"assignedAt":"2025-05-24T16:31:19.729Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-05-24T16:31:19.729Z"}},"_id":"agent_specializations"},"1":{"agentId":"b1d0fe44-5c25-4a60-8696-5fefec4236e1","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T17:53:23.197Z","performance":{"successRate":0,"taskCount":0,"averageTaskDuration":0,"lastEvaluation":"2025-06-30T17:53:23.197Z"}},"2":{"agentId":"aff51406-12b7-46ab-a1e5-a50dea02a464","roleId":"executor","proficiency":50,"assignedAt":"2025-06-30T19:26:55.855Z","performance":{"successRate":0,"'... 93020 more characters,
2025-07-12 00:24:29.706 |       allowAbsoluteUrls: true,
2025-07-12 00:24:29.706 |       _requestStartTime: 1752294268552,
2025-07-12 00:24:29.706 |       _retryCount: 1
2025-07-12 00:24:29.706 |     },
2025-07-12 00:24:29.706 |     request: <ref *1> ClientRequest {
2025-07-12 00:24:29.706 |       _events: [Object: null prototype],
2025-07-12 00:24:29.706 |       _eventsCount: 7,
2025-07-12 00:24:29.706 |       _maxListeners: undefined,
2025-07-12 00:24:29.706 |       outputData: [],
2025-07-12 00:24:29.706 |       outputSize: 0,
2025-07-12 00:24:29.706 |       writable: true,
2025-07-12 00:24:29.706 |       destroyed: true,
2025-07-12 00:24:29.706 |       _last: false,
2025-07-12 00:24:29.707 |       chunkedEncoding: false,
2025-07-12 00:24:29.707 |       shouldKeepAlive: true,
2025-07-12 00:24:29.707 |       maxRequestsOnConnectionReached: false,
2025-07-12 00:24:29.707 |       _defaultKeepAlive: true,
2025-07-12 00:24:29.707 |       useChunkedEncodingByDefault: true,
2025-07-12 00:24:29.707 |       sendDate: false,
2025-07-12 00:24:29.707 |       _removedConnection: false,
2025-07-12 00:24:29.707 |       _removedContLen: false,
2025-07-12 00:24:29.707 |       _removedTE: false,
2025-07-12 00:24:29.707 |       strictContentLength: false,
2025-07-12 00:24:29.707 |       _contentLength: '103020',
2025-07-12 00:24:29.707 |       _hasBody: true,
2025-07-12 00:24:29.707 |       _trailer: '',
2025-07-12 00:24:29.707 |       finished: true,
2025-07-12 00:24:29.707 |       _headerSent: true,
2025-07-12 00:24:29.707 |       _closed: true,
2025-07-12 00:24:29.707 |       socket: [Socket],
2025-07-12 00:24:29.707 |       _header: 'POST /storeData HTTP/1.1\r\n' +
2025-07-12 00:24:29.707 |         'Accept: application/json, text/plain, */*\r\n' +
2025-07-12 00:24:29.707 |         'Content-Type: application/json\r\n' +
2025-07-12 00:24:29.707 |         'Access-Control-Allow-Origin: *\r\n' +
2025-07-12 00:24:29.707 |         'X-Request-ID: 6bio3qiw396\r\n' +
2025-07-12 00:24:29.707 |         'Authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n' +
2025-07-12 00:24:29.707 |         'User-Agent: axios/1.9.0\r\n' +
2025-07-12 00:24:29.707 |         'Content-Length: 103020\r\n' +
2025-07-12 00:24:29.707 |         'Accept-Encoding: gzip, compress, deflate, br\r\n' +
2025-07-12 00:24:29.707 |         'Host: librarian:5040\r\n' +
2025-07-12 00:24:29.707 |         'Connection: keep-alive\r\n' +
2025-07-12 00:24:29.707 |         '\r\n',
2025-07-12 00:24:29.707 |       _keepAliveTimeout: 0,
2025-07-12 00:24:29.707 |       _onPendingData: [Function: nop],
2025-07-12 00:24:29.707 |       agent: [Agent],
2025-07-12 00:24:29.707 |       socketPath: undefined,
2025-07-12 00:24:29.707 |       method: 'POST',
2025-07-12 00:24:29.707 |       maxHeaderSize: undefined,
2025-07-12 00:24:29.707 |       insecureHTTPParser: undefined,
2025-07-12 00:24:29.707 |       joinDuplicateHeaders: undefined,
2025-07-12 00:24:29.707 |       path: '/storeData',
2025-07-12 00:24:29.707 |       _ended: true,
2025-07-12 00:24:29.707 |       res: [IncomingMessage],
2025-07-12 00:24:29.707 |       aborted: false,
2025-07-12 00:24:29.707 |       timeoutCb: null,
2025-07-12 00:24:29.707 |       upgradeOrConnect: false,
2025-07-12 00:24:29.707 |       parser: null,
2025-07-12 00:24:29.707 |       maxHeadersCount: null,
2025-07-12 00:24:29.707 |       reusedSocket: true,
2025-07-12 00:24:29.707 |       host: 'librarian',
2025-07-12 00:24:29.707 |       protocol: 'http:',
2025-07-12 00:24:29.707 |       _redirectable: [Writable],
2025-07-12 00:24:29.707 |       [Symbol(shapeMode)]: false,
2025-07-12 00:24:29.707 |       [Symbol(kCapture)]: false,
2025-07-12 00:24:29.707 |       [Symbol(kBytesWritten)]: 0,
2025-07-12 00:24:29.707 |       [Symbol(kNeedDrain)]: false,
2025-07-12 00:24:29.707 |       [Symbol(corked)]: 0,
2025-07-12 00:24:29.707 |       [Symbol(kOutHeaders)]: [Object: null prototype],
2025-07-12 00:24:29.707 |       [Symbol(errored)]: null,
2025-07-12 00:24:29.707 |       [Symbol(kHighWaterMark)]: 16384,
2025-07-12 00:24:29.707 |       [Symbol(kRejectNonStandardBodyWrites)]: false,
2025-07-12 00:24:29.707 |       [Symbol(kUniqueHeaders)]: null
2025-07-12 00:24:29.707 |     },
2025-07-12 00:24:29.707 |     data: '<!DOCTYPE html>\n' +
2025-07-12 00:24:29.707 |       '<html lang="en">\n' +
2025-07-12 00:24:29.707 |       '<head>\n' +
2025-07-12 00:24:29.707 |       '<meta charset="utf-8">\n' +
2025-07-12 00:24:29.707 |       '<title>Error</title>\n' +
2025-07-12 00:24:29.707 |       '</head>\n' +
2025-07-12 00:24:29.707 |       '<body>\n' +
2025-07-12 00:24:29.707 |       '<pre>Payload Too Large</pre>\n' +
2025-07-12 00:24:29.707 |       '</body>\n' +
2025-07-12 00:24:29.707 |       '</html>\n'
2025-07-12 00:24:29.707 |   },
2025-07-12 00:24:29.707 |   status: 413
2025-07-12 00:24:29.707 | }
2025-07-12 00:24:29.711 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:29.712 | Created new agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with role creative
2025-07-12 00:24:29.730 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:29.766 | AgentSet received update from agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with status running
2025-07-12 00:24:29.841 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:33.767 | Loading work product for agent b31d7887-fc71-4b2b-b1a9-5206e575be13, step 2fdc4018-9e8b-428f-bb81-5e5fd1c30859
2025-07-12 00:24:33.785 | Loading work product for agent c66266ad-88ed-4844-8929-78db18015b49, step 1f9951e6-441c-4149-a10e-de0501942782
2025-07-12 00:24:33.789 | Event logged successfully: {"eventType":"step_result","stepId":"2fdc4018-9e8b-428f-bb81-5e5fd1c30859","stepNo":1,"actionVerb":"ACCOMPLISH","status":"completed","result":[{"success":true,"name":"plan","resultType":"plan","resultDescription":"A plan to: Act as a creative agent","result":[{"actionVerb":"THINK","inputReferences":{"prompt":{"value":"What does it mean to act as a creative agent?","valueType":"any"}},"description":"Clarify the role and responsibilities of a creative agent","outputs":{"understanding":"Understanding of the role of a creative agent"},"dependencies":{},"recommendedRole":"executor"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"idea_generation","valueType":"any"},"prompt":{"value":{"outputName":"understanding","valueType":"string"},"valueType":"any"}},"description":"Generate initial ideas for creative agent actions","outputs":{"ideas":"List of ideas for acting as a creative agent"},"dependencies":{"understanding":1},"recommendedRole":"executor"},{"actionVerb":"ANALYZE","inputReferences":{"data":{"value":{"outputName":"ideas","valueType":"string"},"valueType":"any"}},"description":"Analyze and refine the generated ideas","outputs":{"analysis":"Analysis of generated ideas"},"dependencies":{"ideas":2},"recommendedRole":"executor"},{"actionVerb":"DECIDE","inputReferences":{"condition":{"value":"feasible","valueType":"any"},"trueSteps":{"value":[{"number":5,"actionVerb":"DELEGATE","inputs":{},"outputs":{"task_allocation":"Tasks allocated to sub-agents"},"description":"Delegate tasks to sub-agents"}],"valueType":"any"},"falseSteps":{"value":[{"number":6,"actionVerb":"REPEAT","inputs":{"steps":{"value":[{"number":6.1,"actionVerb":"GENERATE","inputs":{"ConversationType":{"value":"idea_generation","valueType":"string"},"prompt":{"outputName":"understanding","valueType":"string"}},"outputs":{"refined_ideas":"Refined list of ideas"},"description":"Generate refined ideas"}],"valueType":"array"}},"outputs":{"refined_ideas":"Refined ideas"},"description":"Repeat the idea generation process"}],"valueType":"any"}},"description":"Decide on the next course of action","outputs":{"step_results":"Results from step execution"},"dependencies":{"analysis":3},"recommendedRole":"executor"}],"mimeType":"application/json","logs":"2025-07-12 04:24:30,160 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': {'inputValue': 'Act as a creative agent', 'inputName': 'goal', 'args': {}}, 'valueType': 'string', 'args': {}}\n2025-07-12 04:24:30,165 - INFO - Extracted goal from nested 'inputValue': Act as a creative agent\n2025-07-12 04:24:30,166 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- GET_USER_INPUT: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-12 04:24:30,167 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-12 04:24:30,167 - INFO - Querying Brain at brain:5070/chat with prompt length: 7190 chars\n2025-07-12 04:24:31,568 - INFO - Brain query successful with accuracy/text/code\n2025-07-12 04:24:31,570 - INFO - Model response received (attempt 1): {'type': 'PLAN', 'plan': [{'number': 1, 'actionVerb': 'THINK', 'inputs': {'prompt': {'value': 'What does it mean to act as a creative agent?', 'valueType': 'string'}}, 'outputs': {'understanding': 'Understanding of the role of a creative agent'}, 'description': 'Clarify the role and responsibilities of a creative agent'}, {'number': 2, 'actionVerb': 'GENERATE', 'inputs': {'ConversationType': {'value': 'idea_generation', 'valueType': 'string'}, 'prompt': {'outputName': 'understanding', 'valueType...\n2025-07-12 04:24:31,575 - INFO - Successfully parsed top-level PLAN object. Plan length: 4\n2025-07-12 04:24:31,576 - INFO - Auto-fixed: added missing 'dependencies' field for step 1\n2025-07-12 04:24:31,576 - INFO - Auto-fixed: set default 'recommendedRole' for step 1\n2025-07-12 04:24:31,576 - INFO - Auto-fixed: set default 'recommendedRole' for step 2\n2025-07-12 04:24:31,577 - INFO - Auto-fixed: set default 'recommendedRole' for step 3\n2025-07-12 04:24:31,577 - INFO - Auto-fixed: set default 'recommendedRole' for step 4\n2025-07-12 04:24:31,577 - INFO - Auto-fixing input 'condition' in step 4: removing outputName, keeping value\n2025-07-12 04:24:31,577 - WARNING - Plan validation failed: Step 4 input 'trueSteps' is not an object. Expected {'value': '...'} or {'outputName': '...'}.. Attempting auto-repair (repair attempt 1).\n2025-07-12 04:24:31,577 - INFO - Auto-repairing plan with focused prompt...\n2025-07-12 04:24:31,578 - INFO - Detected input schema compliance issue, using specialized repair prompt\n2025-07-12 04:24:31,579 - INFO - Querying Brain at brain:5070/chat with prompt length: 3189 chars\n2025-07-12 04:24:33,467 - INFO - Brain query successful with accuracy/text/code\n2025-07-12 04:24:33,469 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 1\n2025-07-12 04:24:33,469 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 2\n2025-07-12 04:24:33,469 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 3\n2025-07-12 04:24:33,470 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 4\n2025-07-12 04:24:33,492 - INFO - Successfully reported plan generation success to Brain (quality: 73)\n2025-07-12 04:24:33,502 - INFO - Successfully processed plan for goal: Act as a creative agent\n"}],"dependencies":[],"timestamp":"2025-07-12T04:24:33.749Z"}
2025-07-12 00:24:33.789 | Saving work product for agent 2fdc4018-9e8b-428f-bb81-5e5fd1c30859, step 2fdc4018-9e8b-428f-bb81-5e5fd1c30859
2025-07-12 00:24:33.816 | [AuthenticatedAxios] Request i98haq67b6n: Failed after 41ms: {
2025-07-12 00:24:33.816 |   status: 404,
2025-07-12 00:24:33.816 |   statusText: 'Not Found',
2025-07-12 00:24:33.816 |   data: { error: 'Work product not found' },
2025-07-12 00:24:33.816 |   url: 'http://librarian:5040/loadWorkProduct/2fdc4018-9e8b-428f-bb81-5e5fd1c30859'
2025-07-12 00:24:33.816 | }
2025-07-12 00:24:33.831 | Error loading work product b31d7887-fc71-4b2b-b1a9-5206e575be13_2fdc4018-9e8b-428f-bb81-5e5fd1c30859: Request failed with status code 404
2025-07-12 00:24:33.831 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 has completed its work.
2025-07-12 00:24:33.831 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 saying: Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 has completed its work.
2025-07-12 00:24:33.831 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type say to user
2025-07-12 00:24:33.837 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 saying: Result: null
2025-07-12 00:24:33.837 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type say to user
2025-07-12 00:24:33.837 | Step ACCOMPLISH result: [
2025-07-12 00:24:33.837 |   {
2025-07-12 00:24:33.837 |     success: true,
2025-07-12 00:24:33.837 |     name: 'plan',
2025-07-12 00:24:33.837 |     resultType: 'plan',
2025-07-12 00:24:33.837 |     resultDescription: 'A plan to: Act as a creative agent',
2025-07-12 00:24:33.837 |     result: [ [Object], [Object], [Object], [Object] ],
2025-07-12 00:24:33.838 |     mimeType: 'application/json',
2025-07-12 00:24:33.838 |     logs: "2025-07-12 04:24:30,160 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': {'inputValue': 'Act as a creative agent', 'inputName': 'goal', 'args': {}}, 'valueType': 'string', 'args': {}}\n" +
2025-07-12 00:24:33.838 |       "2025-07-12 04:24:30,165 - INFO - Extracted goal from nested 'inputValue': Act as a creative agent\n" +
2025-07-12 00:24:33.839 |       `2025-07-12 04:24:30,166 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- GET_USER_INPUT: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n` +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:30,167 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n" +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:30,167 - INFO - Querying Brain at brain:5070/chat with prompt length: 7190 chars\n' +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:31,568 - INFO - Brain query successful with accuracy/text/code\n' +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,570 - INFO - Model response received (attempt 1): {'type': 'PLAN', 'plan': [{'number': 1, 'actionVerb': 'THINK', 'inputs': {'prompt': {'value': 'What does it mean to act as a creative agent?', 'valueType': 'string'}}, 'outputs': {'understanding': 'Understanding of the role of a creative agent'}, 'description': 'Clarify the role and responsibilities of a creative agent'}, {'number': 2, 'actionVerb': 'GENERATE', 'inputs': {'ConversationType': {'value': 'idea_generation', 'valueType': 'string'}, 'prompt': {'outputName': 'understanding', 'valueType...\n" +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:31,575 - INFO - Successfully parsed top-level PLAN object. Plan length: 4\n' +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,576 - INFO - Auto-fixed: added missing 'dependencies' field for step 1\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,576 - INFO - Auto-fixed: set default 'recommendedRole' for step 1\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,576 - INFO - Auto-fixed: set default 'recommendedRole' for step 2\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,577 - INFO - Auto-fixed: set default 'recommendedRole' for step 3\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,577 - INFO - Auto-fixed: set default 'recommendedRole' for step 4\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,577 - INFO - Auto-fixing input 'condition' in step 4: removing outputName, keeping value\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:31,577 - WARNING - Plan validation failed: Step 4 input 'trueSteps' is not an object. Expected {'value': '...'} or {'outputName': '...'}.. Attempting auto-repair (repair attempt 1).\n" +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:31,577 - INFO - Auto-repairing plan with focused prompt...\n' +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:31,578 - INFO - Detected input schema compliance issue, using specialized repair prompt\n' +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:31,579 - INFO - Querying Brain at brain:5070/chat with prompt length: 3189 chars\n' +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:33,467 - INFO - Brain query successful with accuracy/text/code\n' +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:33,469 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 1\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:33,469 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 2\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:33,469 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 3\n" +
2025-07-12 00:24:33.840 |       "2025-07-12 04:24:33,470 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 4\n" +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:33,492 - INFO - Successfully reported plan generation success to Brain (quality: 73)\n' +
2025-07-12 00:24:33.840 |       '2025-07-12 04:24:33,502 - INFO - Successfully processed plan for goal: Act as a creative agent\n'
2025-07-12 00:24:33.840 |   }
2025-07-12 00:24:33.840 | ]
2025-07-12 00:24:33.840 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 saying: Completed step: ACCOMPLISH
2025-07-12 00:24:33.840 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type say to user
2025-07-12 00:24:33.840 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 saying: Generated a plan (direct array) with 4 steps
2025-07-12 00:24:33.840 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type say to user
2025-07-12 00:24:33.840 | [Agent b31d7887-fc71-4b2b-b1a9-5206e575be13] Parsed plan for addStepsFromPlan: [{"actionVerb":"THINK","inputReferences":{"prompt":{"value":"What does it mean to act as a creative agent?","valueType":"any"}},"description":"Clarify the role and responsibilities of a creative agent","outputs":{"understanding":"Understanding of the role of a creative agent"},"dependencies":{},"recommendedRole":"executor"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"idea_generation","valueType":"any"},"prompt":{"value":{"outputName":"understanding","valueType":"string"},"valueType":"any"}},"description":"Generate initial ideas for creative agent actions","outputs":{"ideas":"List of ideas for acting as a creative agent"},"dependencies":{"understanding":1},"recommendedRole":"executor"},{"actionVerb":"ANALYZE","inputReferences":{"data":{"value":{"outputName":"ideas","valueType":"string"},"valueType":"any"}},"description":"Analyze and refine the generated ideas","outputs":{"analysis":"Analysis of generated ideas"},"dependencies":{"ideas":2},"recommendedRole":"executor"},{"actionVerb":"DECIDE","inputReferences":{"condition":{"value":"feasible","valueType":"any"},"trueSteps":{"value":[{"number":5,"actionVerb":"DELEGATE","inputs":{},"outputs":{"task_allocation":"Tasks allocated to sub-agents"},"description":"Delegate tasks to sub-agents"}],"valueType":"any"},"falseSteps":{"value":[{"number":6,"actionVerb":"REPEAT","inputs":{"steps":{"value":[{"number":6.1,"actionVerb":"GENERATE","inputs":{"ConversationType":{"value":"idea_generation","valueType":"string"},"prompt":{"outputName":"understanding","valueType":"string"}},"outputs":{"refined_ideas":"Refined list of ideas"},"description":"Generate refined ideas"}],"valueType":"array"}},"outputs":{"refined_ideas":"Refined ideas"},"description":"Repeat the idea generation process"}],"valueType":"any"}},"description":"Decide on the next course of action","outputs":{"step_results":"Results from step execution"},"dependencies":{"analysis":3},"recommendedRole":"executor"}]
2025-07-12 00:24:33.845 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 notifying TrafficManager of status: completed
2025-07-12 00:24:33.847 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:33.880 | Error analysis already in progress, skipping
2025-07-12 00:24:33.880 | Agent c66266ad-88ed-4844-8929-78db18015b49 has completed its work.
2025-07-12 00:24:33.880 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 saying: Agent c66266ad-88ed-4844-8929-78db18015b49 has completed its work.
2025-07-12 00:24:33.883 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type say to user
2025-07-12 00:24:33.883 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 saying: Result: null
2025-07-12 00:24:33.883 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type say to user
2025-07-12 00:24:33.884 | [AuthenticatedAxios] Request 4wylojhxgad: Failed after 95ms: {
2025-07-12 00:24:33.885 |   status: 404,
2025-07-12 00:24:33.885 |   statusText: 'Not Found',
2025-07-12 00:24:33.885 |   data: { error: 'Work product not found' },
2025-07-12 00:24:33.885 |   url: 'http://librarian:5040/loadWorkProduct/1f9951e6-441c-4149-a10e-de0501942782'
2025-07-12 00:24:33.885 | }
2025-07-12 00:24:33.885 | Error loading work product c66266ad-88ed-4844-8929-78db18015b49_1f9951e6-441c-4149-a10e-de0501942782: Request failed with status code 404
2025-07-12 00:24:33.890 | Event logged successfully: {"eventType":"step_result","stepId":"1f9951e6-441c-4149-a10e-de0501942782","stepNo":1,"actionVerb":"ACCOMPLISH","status":"completed","result":[{"success":true,"name":"plan","resultType":"plan","resultDescription":"A plan to: Act as a researcher agent","result":[{"actionVerb":"GENERATE","inputReferences":{},"description":"Generate definition of a researcher agent","outputs":{"researcherAgentDefinition":"Definition of a researcher agent"},"dependencies":{},"recommendedRole":"researcher"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"research","valueType":"any"},"prompt":{"value":"researcherAgentDefinition","valueType":"any"}},"description":"Generate an initial research plan based on the definition of a researcher agent","outputs":{"researchPlan":"Initial research plan"},"dependencies":{},"recommendedRole":"executor"}],"mimeType":"application/json","logs":"2025-07-12 04:24:29,311 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': {'inputValue': 'Act as a researcher agent', 'inputName': 'goal', 'args': {}}, 'valueType': 'string', 'args': {}}\n2025-07-12 04:24:29,311 - INFO - Extracted goal from nested 'inputValue': Act as a researcher agent\n2025-07-12 04:24:29,312 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- GET_USER_INPUT: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-12 04:24:29,312 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-12 04:24:29,312 - INFO - Querying Brain at brain:5070/chat with prompt length: 7192 chars\n2025-07-12 04:24:30,965 - INFO - Brain query successful with accuracy/text/code\n2025-07-12 04:24:30,968 - INFO - Model response received (attempt 1): {'type': 'PLAN', 'plan': [{'number': 1, 'actionVerb': 'THINK', 'inputs': {'prompt': {'value': 'What does it mean to act as a researcher agent?', 'valueType': 'string'}}, 'outputs': {'researcherAgentDefinition': 'Definition of a researcher agent'}, 'description': 'Understand the role of a researcher agent'}, {'number': 2, 'actionVerb': 'GENERATE', 'inputs': {'ConversationType': {'value': 'research', 'valueType': 'string'}, 'prompt': {'outputName': 'researcherAgentDefinition', 'valueType': 'string...\n2025-07-12 04:24:30,968 - INFO - Successfully parsed top-level PLAN object. Plan length: 4\n2025-07-12 04:24:30,968 - INFO - Auto-fixed: added missing 'dependencies' field for step 1\n2025-07-12 04:24:30,968 - INFO - Auto-fixed: set default 'recommendedRole' for step 1\n2025-07-12 04:24:30,977 - INFO - Auto-fixed: set default 'recommendedRole' for step 2\n2025-07-12 04:24:30,977 - INFO - Auto-fixed: set default 'recommendedRole' for step 3\n2025-07-12 04:24:30,977 - WARNING - Plan validation failed: Step 3 input 'trueSteps' is not an object. Expected {'value': '...'} or {'outputName': '...'}.. Attempting auto-repair (repair attempt 1).\n2025-07-12 04:24:30,978 - INFO - Auto-repairing plan with focused prompt...\n2025-07-12 04:24:30,978 - INFO - Detected input schema compliance issue, using specialized repair prompt\n2025-07-12 04:24:30,979 - INFO - Querying Brain at brain:5070/chat with prompt length: 3428 chars\n2025-07-12 04:24:32,800 - INFO - Brain query successful with accuracy/text/code\n2025-07-12 04:24:32,800 - INFO - Auto-repair returned single step, wrapping in array\n2025-07-12 04:24:32,801 - WARNING - Plan validation failed: Step 1 has invalid dependency: step 1 for output 'researcherAgentDefinition'. Dependencies must reference previous steps only.. Attempting auto-repair (repair attempt 2).\n2025-07-12 04:24:32,801 - INFO - Auto-repairing plan with focused prompt...\n2025-07-12 04:24:32,804 - INFO - Querying Brain at brain:5070/chat with prompt length: 1112 chars\n2025-07-12 04:24:33,530 - INFO - Brain query successful with accuracy/text/code\n2025-07-12 04:24:33,534 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 1\n2025-07-12 04:24:33,535 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 2\n2025-07-12 04:24:33,556 - INFO - Successfully reported plan generation success to Brain (quality: 69)\n2025-07-12 04:24:33,559 - INFO - Successfully processed plan for goal: Act as a researcher agent\n"}],"dependencies":[],"timestamp":"2025-07-12T04:24:33.777Z"}
2025-07-12 00:24:33.894 | Saving work product for agent 1f9951e6-441c-4149-a10e-de0501942782, step 1f9951e6-441c-4149-a10e-de0501942782
2025-07-12 00:24:33.911 | Event logged successfully: {"eventType":"step_created","stepId":"85b7b179-b863-4ce8-8e0d-46b5dd9f4c80","stepNo":2,"actionVerb":"THINK","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["prompt",{"inputName":"prompt","value":"What does it mean to act as a creative agent?","valueType":"any"}]]},"dependencies":[],"status":"pending","description":"Clarify the role and responsibilities of a creative agent","recommendedRole":"executor","timestamp":"2025-07-12T04:24:33.841Z"}
2025-07-12 00:24:33.911 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:33.911 | Successfully sent message to PostOffice: Result: null
2025-07-12 00:24:33.938 | Event logged successfully: {"eventType":"step_created","stepId":"9ca56f7c-d550-493a-862a-15f8fd92e636","stepNo":3,"actionVerb":"GENERATE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["ConversationType",{"inputName":"ConversationType","value":"idea_generation","valueType":"any"}],["prompt",{"inputName":"prompt","value":{"outputName":"understanding","valueType":"string"},"valueType":"any"}]]},"dependencies":[{"outputName":"understanding","sourceStepId":"85b7b179-b863-4ce8-8e0d-46b5dd9f4c80","inputName":"understanding"}],"status":"pending","description":"Generate initial ideas for creative agent actions","recommendedRole":"executor","timestamp":"2025-07-12T04:24:33.842Z"}
2025-07-12 00:24:33.938 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:33.938 | Successfully sent message to PostOffice: Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 has completed its work.
2025-07-12 00:24:34.109 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.109 | Successfully sent message to PostOffice: Completed step: ACCOMPLISH
2025-07-12 00:24:34.122 | Step ACCOMPLISH result: [
2025-07-12 00:24:34.122 |   {
2025-07-12 00:24:34.122 |     success: true,
2025-07-12 00:24:34.122 |     name: 'plan',
2025-07-12 00:24:34.122 |     resultType: 'plan',
2025-07-12 00:24:34.122 |     resultDescription: 'A plan to: Act as a researcher agent',
2025-07-12 00:24:34.122 |     result: [ [Object], [Object] ],
2025-07-12 00:24:34.122 |     mimeType: 'application/json',
2025-07-12 00:24:34.122 |     logs: "2025-07-12 04:24:29,311 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': {'inputValue': 'Act as a researcher agent', 'inputName': 'goal', 'args': {}}, 'valueType': 'string', 'args': {}}\n" +
2025-07-12 00:24:34.122 |       "2025-07-12 04:24:29,311 - INFO - Extracted goal from nested 'inputValue': Act as a researcher agent\n" +
2025-07-12 00:24:34.123 |       `2025-07-12 04:24:29,312 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- GET_USER_INPUT: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n` +
2025-07-12 00:24:34.123 |       "2025-07-12 04:24:29,312 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n" +
2025-07-12 00:24:34.123 |       '2025-07-12 04:24:29,312 - INFO - Querying Brain at brain:5070/chat with prompt length: 7192 chars\n' +
2025-07-12 00:24:34.123 |       '2025-07-12 04:24:30,965 - INFO - Brain query successful with accuracy/text/code\n' +
2025-07-12 00:24:34.123 |       "2025-07-12 04:24:30,968 - INFO - Model response received (attempt 1): {'type': 'PLAN', 'plan': [{'number': 1, 'actionVerb': 'THINK', 'inputs': {'prompt': {'value': 'What does it mean to act as a researcher agent?', 'valueType': 'string'}}, 'outputs': {'researcherAgentDefinition': 'Definition of a researcher agent'}, 'description': 'Understand the role of a researcher agent'}, {'number': 2, 'actionVerb': 'GENERATE', 'inputs': {'ConversationType': {'value': 'research', 'valueType': 'string'}, 'prompt': {'outputName': 'researcherAgentDefinition', 'valueType': 'string...\n" +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:30,968 - INFO - Successfully parsed top-level PLAN object. Plan length: 4\n' +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:30,968 - INFO - Auto-fixed: added missing 'dependencies' field for step 1\n" +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:30,968 - INFO - Auto-fixed: set default 'recommendedRole' for step 1\n" +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:30,977 - INFO - Auto-fixed: set default 'recommendedRole' for step 2\n" +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:30,977 - INFO - Auto-fixed: set default 'recommendedRole' for step 3\n" +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:30,977 - WARNING - Plan validation failed: Step 3 input 'trueSteps' is not an object. Expected {'value': '...'} or {'outputName': '...'}.. Attempting auto-repair (repair attempt 1).\n" +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:30,978 - INFO - Auto-repairing plan with focused prompt...\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:30,978 - INFO - Detected input schema compliance issue, using specialized repair prompt\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:30,979 - INFO - Querying Brain at brain:5070/chat with prompt length: 3428 chars\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:32,800 - INFO - Brain query successful with accuracy/text/code\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:32,800 - INFO - Auto-repair returned single step, wrapping in array\n' +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:32,801 - WARNING - Plan validation failed: Step 1 has invalid dependency: step 1 for output 'researcherAgentDefinition'. Dependencies must reference previous steps only.. Attempting auto-repair (repair attempt 2).\n" +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:32,801 - INFO - Auto-repairing plan with focused prompt...\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:32,804 - INFO - Querying Brain at brain:5070/chat with prompt length: 1112 chars\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:33,530 - INFO - Brain query successful with accuracy/text/code\n' +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:33,534 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 1\n" +
2025-07-12 00:24:34.138 |       "2025-07-12 04:24:33,535 - INFO - Auto-fixed: copied 'verb' to 'actionVerb' for step 2\n" +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:33,556 - INFO - Successfully reported plan generation success to Brain (quality: 69)\n' +
2025-07-12 00:24:34.138 |       '2025-07-12 04:24:33,559 - INFO - Successfully processed plan for goal: Act as a researcher agent\n'
2025-07-12 00:24:34.138 |   }
2025-07-12 00:24:34.147 | ]
2025-07-12 00:24:34.147 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 saying: Completed step: ACCOMPLISH
2025-07-12 00:24:34.147 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type say to user
2025-07-12 00:24:34.147 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 saying: Generated a plan (direct array) with 2 steps
2025-07-12 00:24:34.147 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type say to user
2025-07-12 00:24:34.147 | [Agent c66266ad-88ed-4844-8929-78db18015b49] Parsed plan for addStepsFromPlan: [{"actionVerb":"GENERATE","inputReferences":{},"description":"Generate definition of a researcher agent","outputs":{"researcherAgentDefinition":"Definition of a researcher agent"},"dependencies":{},"recommendedRole":"researcher"},{"actionVerb":"GENERATE","inputReferences":{"ConversationType":{"value":"research","valueType":"any"},"prompt":{"value":"researcherAgentDefinition","valueType":"any"}},"description":"Generate an initial research plan based on the definition of a researcher agent","outputs":{"researchPlan":"Initial research plan"},"dependencies":{},"recommendedRole":"executor"}]
2025-07-12 00:24:34.147 | Agent c66266ad-88ed-4844-8929-78db18015b49 notifying TrafficManager of status: completed
2025-07-12 00:24:34.147 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:34.147 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.147 | Successfully sent message to PostOffice: Generated a plan (direct array) with 4 steps
2025-07-12 00:24:34.147 | Event logged successfully: {"eventType":"step_created","stepId":"a64dc12b-97e4-488e-af35-b26364c854ee","stepNo":4,"actionVerb":"ANALYZE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["data",{"inputName":"data","value":{"outputName":"ideas","valueType":"string"},"valueType":"any"}]]},"dependencies":[{"outputName":"ideas","sourceStepId":"9ca56f7c-d550-493a-862a-15f8fd92e636","inputName":"ideas"}],"status":"pending","description":"Analyze and refine the generated ideas","recommendedRole":"executor","timestamp":"2025-07-12T04:24:33.843Z"}
2025-07-12 00:24:34.147 | Event logged successfully: {"eventType":"step_created","stepId":"31f11d75-084d-47f2-8b56-aed16f65ccbc","stepNo":5,"actionVerb":"DECIDE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["condition",{"inputName":"condition","value":"feasible","valueType":"any"}],["trueSteps",{"inputName":"trueSteps","value":[{"number":5,"actionVerb":"DELEGATE","inputs":{},"outputs":{"task_allocation":"Tasks allocated to sub-agents"},"description":"Delegate tasks to sub-agents"}],"valueType":"any"}],["falseSteps",{"inputName":"falseSteps","value":[{"number":6,"actionVerb":"REPEAT","inputs":{"steps":{"value":[{"number":6.1,"actionVerb":"GENERATE","inputs":{"ConversationType":{"value":"idea_generation","valueType":"string"},"prompt":{"outputName":"understanding","valueType":"string"}},"outputs":{"refined_ideas":"Refined list of ideas"},"description":"Generate refined ideas"}],"valueType":"array"}},"outputs":{"refined_ideas":"Refined ideas"},"description":"Repeat the idea generation process"}],"valueType":"any"}]]},"dependencies":[{"outputName":"analysis","sourceStepId":"a64dc12b-97e4-488e-af35-b26364c854ee","inputName":"analysis"}],"status":"pending","description":"Decide on the next course of action","recommendedRole":"executor","timestamp":"2025-07-12T04:24:33.844Z"}
2025-07-12 00:24:34.155 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.155 | Successfully sent message to PostOffice: Agent c66266ad-88ed-4844-8929-78db18015b49 has completed its work.
2025-07-12 00:24:34.155 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:34.167 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.167 | Successfully sent message to PostOffice: Result: null
2025-07-12 00:24:34.176 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.176 | Successfully sent message to PostOffice: Completed step: ACCOMPLISH
2025-07-12 00:24:34.213 | Event logged successfully: {"eventType":"step_created","stepId":"b0820203-6059-4db1-887b-0930f1dd6b61","stepNo":2,"actionVerb":"GENERATE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[]},"dependencies":[],"status":"pending","description":"Generate definition of a researcher agent","recommendedRole":"researcher","timestamp":"2025-07-12T04:24:34.117Z"}
2025-07-12 00:24:34.223 | Event logged successfully: {"eventType":"step_created","stepId":"55c1b015-d552-4baf-9b3d-e48d77668adb","stepNo":3,"actionVerb":"GENERATE","inputValues":{"_type":"Map","entries":[]},"inputReferences":{"_type":"Map","entries":[["ConversationType",{"inputName":"ConversationType","value":"research","valueType":"any"}],["prompt",{"inputName":"prompt","value":"researcherAgentDefinition","valueType":"any"}]]},"dependencies":[],"status":"pending","description":"Generate an initial research plan based on the definition of a researcher agent","recommendedRole":"executor","timestamp":"2025-07-12T04:24:34.121Z"}
2025-07-12 00:24:34.224 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.224 | Successfully sent message to PostOffice: Generated a plan (direct array) with 2 steps
2025-07-12 00:24:34.225 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:34.241 | AgentSet received update from agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with status completed
2025-07-12 00:24:34.246 | AgentSet received update from agent c66266ad-88ed-4844-8929-78db18015b49 with status completed
2025-07-12 00:24:34.310 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:34.320 | Saving work product for agent b31d7887-fc71-4b2b-b1a9-5206e575be13, step 2fdc4018-9e8b-428f-bb81-5e5fd1c30859
2025-07-12 00:24:34.324 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:34.328 | Saving work product for agent c66266ad-88ed-4844-8929-78db18015b49, step 1f9951e6-441c-4149-a10e-de0501942782
2025-07-12 00:24:34.352 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:34.352 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type workProductUpdate to user
2025-07-12 00:24:34.353 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 notifying TrafficManager of status: completed
2025-07-12 00:24:34.353 | AgentSet b31d7887-fc71-4b2b-b1a9-5206e575be13 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:34.361 | Service Librarian discovered via service discovery: librarian:5040
2025-07-12 00:24:34.361 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type workProductUpdate to user
2025-07-12 00:24:34.362 | Agent c66266ad-88ed-4844-8929-78db18015b49 notifying TrafficManager of status: completed
2025-07-12 00:24:34.364 | AgentSet c66266ad-88ed-4844-8929-78db18015b49 sending message of type agentUpdate to trafficmanager
2025-07-12 00:24:34.371 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.371 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:34.382 | Successfully sent message to user via HTTP. Response status: 200
2025-07-12 00:24:34.383 | AgentSet received update from agent b31d7887-fc71-4b2b-b1a9-5206e575be13 with status completed
2025-07-12 00:24:34.399 | Successfully sent message to trafficmanager via HTTP. Response status: 200
2025-07-12 00:24:34.402 | AgentSet received update from agent c66266ad-88ed-4844-8929-78db18015b49 with status completed
2025-07-12 00:24:34.433 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:34.433 | Agent b31d7887-fc71-4b2b-b1a9-5206e575be13 is not RUNNING, skipping checkAndResumeBlockedAgents.
2025-07-12 00:24:34.434 | Successfully notified AgentSet at agentset:5100
2025-07-12 00:24:34.434 | Agent c66266ad-88ed-4844-8929-78db18015b49 is not RUNNING, skipping checkAndResumeBlockedAgents.
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 | **** REMEDIATION GUIDANCE ****
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 |     Error: Request failed with status code 404
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 |     Stack: AxiosError: Request failed with status code 404
2025-07-12 00:24:34.503 |     at settle (/usr/src/app/node_modules/axios/dist/node/axios.cjs:2049:12)
2025-07-12 00:24:34.503 |     at IncomingMessage.handleStreamEnd (/usr/src/app/node_modules/axios/dist/node/axios.cjs:3166:11)
2025-07-12 00:24:34.503 |     at IncomingMessage.emit (node:events:536:35)
2025-07-12 00:24:34.503 |     at endReadableNT (node:internal/streams/readable:1698:12)
2025-07-12 00:24:34.503 |     at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
2025-07-12 00:24:34.503 |     at Axios.request (/usr/src/app/node_modules/axios/dist/node/axios.cjs:4276:41)
2025-07-12 00:24:34.503 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12 00:24:34.503 |     at async AgentPersistenceManager.loadWorkProduct (/usr/src/app/services/agentset/dist/utils/AgentPersistenceManager.js:122:30)
2025-07-12 00:24:34.503 |     at async Agent.runAgent (/usr/src/app/services/agentset/dist/agents/Agent.js:258:31)
2025-07-12 00:24:34.503 |     at async Agent.runUntilDone (/usr/src/app/services/agentset/dist/agents/Agent.js:99:13)
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 |     Remediation Guidance:
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 |     Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-12 00:24:34.503 | 
2025-07-12 00:24:34.503 | *******************************
2025-07-12 00:40:26.251 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 00:41:08.156 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:41:08.648 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 00:57:06.351 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 00:57:48.214 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 00:57:48.711 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 01:13:34.886 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 01:14:13.940 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 01:14:14.371 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 01:28:39.000 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 01:29:17.601 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 01:29:18.100 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 01:43:39.954 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 01:44:18.464 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 01:44:18.940 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 01:58:41.128 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 01:59:19.689 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 01:59:20.208 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13
2025-07-12 02:13:43.080 | Created checkpoint for agent ded32008-8dc5-49e7-bdc9-d32acba1c461
2025-07-12 02:14:21.594 | Created checkpoint for agent c66266ad-88ed-4844-8929-78db18015b49
2025-07-12 02:14:22.113 | Created checkpoint for agent b31d7887-fc71-4b2b-b1a9-5206e575be13