#!/usr/bin/env python3

import sys
import os

# Add the plugin directory to sys.path to allow direct import of main
plugin_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, plugin_dir)

from main import AccomplishPlugin

def test_dependency_validation():
    plugin = AccomplishPlugin()
    
    # Test case 1: Future step reference
    print("=== Test 1: Future step reference ===")
    plan_data = [
        {
            "number": 1, 
            "actionVerb": "ANALYZE", 
            "description": "Analyze future data",
            "inputs": {"data": {"outputName": "futureResults", "valueType": "string"}}, 
            "outputs": {"analysis": "analysis results"},
            "dependencies": {"futureResults": 2}  # References future step
        },
        {
            "number": 2, 
            "actionVerb": "SEARCH", 
            "description": "Search for cats",
            "inputs": {"searchTerm": {"value": "cats", "valueType": "string"}}, 
            "outputs": {"futureResults": "list of search results"},
            "dependencies": {}
        }
    ]

    print(f"Step 1 dependencies: {plan_data[0]['dependencies']}")
    print(f"Step 2 dependencies: {plan_data[1]['dependencies']}")
    print(f"i=0, dep_step_number=2, i+1=1, condition: {2 >= 1}")

    error = plugin.validate_plan_data(plan_data)
    print(f"Error: {error}")
    print(f"Should fail: {error is not None}")
    
    # Test case 2: Invalid step reference
    print("\n=== Test 2: Invalid step reference ===")
    plan_data = [
        {
            "number": 1, 
            "actionVerb": "SEARCH", 
            "description": "Search for cats",
            "inputs": {"searchTerm": {"value": "cats", "valueType": "string"}}, 
            "outputs": {"searchResults": "list of search results"},
            "dependencies": {}
        },
        {
            "number": 2, 
            "actionVerb": "ANALYZE", 
            "description": "Analyze non-existent data",
            "inputs": {"data": {"outputName": "nonExistentOutput", "valueType": "string"}}, 
            "outputs": {"analysis": "analysis results"},
            "dependencies": {"nonExistentOutput": 1}  # References output that step 1 doesn't produce
        }
    ]
    
    error = plugin.validate_plan_data(plan_data)
    print(f"Error: {error}")
    print(f"Should fail: {error is not None}")
    
    # Test case 3: Valid dependencies
    print("\n=== Test 3: Valid dependencies ===")
    plan_data = [
        {
            "number": 1, 
            "actionVerb": "SEARCH", 
            "description": "Search for cats",
            "inputs": {"searchTerm": {"value": "cats", "valueType": "string"}}, 
            "outputs": {"searchResults": "list of search results"},
            "dependencies": {}
        },
        {
            "number": 2, 
            "actionVerb": "ANALYZE", 
            "description": "Analyze the search results",
            "inputs": {"data": {"outputName": "searchResults", "valueType": "string"}}, 
            "outputs": {"analysis": "analysis results"},
            "dependencies": {"searchResults": 1}  # Correct format
        }
    ]
    
    error = plugin.validate_plan_data(plan_data)
    print(f"Error: {error}")
    print(f"Should pass: {error is None}")

if __name__ == "__main__":
    test_dependency_validation()
